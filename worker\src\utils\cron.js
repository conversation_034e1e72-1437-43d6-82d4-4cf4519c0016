/**
 * Cron utilities for parsing and calculating next run times
 */

import parser from 'cron-parser';

/**
 * Cron utility class
 */
export class CronUtil {
  /**
   * Parses a cron expression and returns the next execution time
   * @param {string} cronExpression - Cron expression (5 fields: minute hour day month weekday)
   * @param {Date} currentTime - Current time (default: now)
   * @param {string} timezone - Timezone (default: UTC)
   * @returns {Date|null} - Next execution time or null if invalid
   */
  static getNextRunTime(cronExpression, currentTime = new Date(), timezone = 'UTC') {
    try {
      const interval = parser.parseExpression(cronExpression, {
        currentDate: currentTime,
        tz: timezone
      });

      return interval.next().toDate();
    } catch (error) {
      console.error('Error parsing cron expression:', error);
      return null;
    }
  }

  /**
   * Gets multiple next execution times
   * @param {string} cronExpression - Cron expression
   * @param {number} count - Number of next times to get
   * @param {Date} currentTime - Current time (default: now)
   * @param {string} timezone - Timezone (default: UTC)
   * @returns {Array<Date>} - Array of next execution times
   */
  static getNextRunTimes(cronExpression, count = 5, currentTime = new Date(), timezone = 'UTC') {
    try {
      const interval = parser.parseExpression(cronExpression, {
        currentDate: currentTime,
        tz: timezone
      });

      const times = [];
      for (let i = 0; i < count; i++) {
        times.push(interval.next().toDate());
      }

      return times;
    } catch (error) {
      console.error('Error parsing cron expression:', error);
      return [];
    }
  }

  /**
   * Checks if a task should run at a given time
   * @param {string} cronExpression - Cron expression
   * @param {Date} checkTime - Time to check
   * @param {string} timezone - Timezone (default: UTC)
   * @returns {boolean} - Whether task should run
   */
  static shouldRunAt(cronExpression, checkTime, timezone = 'UTC') {
    try {
      // Get the previous run time from the check time
      const interval = parser.parseExpression(cronExpression, {
        currentDate: checkTime,
        tz: timezone
      });

      const prevTime = interval.prev().toDate();
      const nextTime = interval.next().toDate();

      // Check if the check time is within 1 minute of the scheduled time
      const timeDiff = Math.abs(checkTime.getTime() - prevTime.getTime());
      return timeDiff < 60000; // 1 minute tolerance

    } catch (error) {
      console.error('Error checking cron schedule:', error);
      return false;
    }
  }

  /**
   * Validates a cron expression
   * @param {string} cronExpression - Cron expression to validate
   * @returns {Object} - Validation result { valid: boolean, error?: string }
   */
  static validateCronExpression(cronExpression) {
    try {
      parser.parseExpression(cronExpression);
      return { valid: true };
    } catch (error) {
      return {
        valid: false,
        error: error.message
      };
    }
  }

  /**
   * Converts a cron expression to human-readable description
   * @param {string} cronExpression - Cron expression
   * @returns {string} - Human-readable description
   */
  static describe(cronExpression) {
    try {
      // Basic description logic - can be enhanced with a library like cronstrue
      const parts = cronExpression.split(' ');
      if (parts.length !== 5) {
        return 'Invalid cron expression';
      }

      const [minute, hour, day, month, weekday] = parts;

      // Simple patterns
      if (cronExpression === '0 0 * * *') {
        return 'Daily at midnight';
      }
      if (cronExpression === '0 12 * * *') {
        return 'Daily at noon';
      }
      if (cronExpression === '0 0 * * 0') {
        return 'Weekly on Sunday at midnight';
      }
      if (cronExpression === '0 0 1 * *') {
        return 'Monthly on the 1st at midnight';
      }
      if (cronExpression === '*/5 * * * *') {
        return 'Every 5 minutes';
      }
      if (cronExpression === '*/15 * * * *') {
        return 'Every 15 minutes';
      }
      if (cronExpression === '0 */1 * * *') {
        return 'Every hour';
      }

      // Generic description
      let description = 'At ';

      if (minute === '*') {
        description += 'every minute';
      } else if (minute.startsWith('*/')) {
        description += `every ${minute.slice(2)} minutes`;
      } else {
        description += `minute ${minute}`;
      }

      if (hour !== '*') {
        if (hour.startsWith('*/')) {
          description += ` of every ${hour.slice(2)} hours`;
        } else {
          description += ` of hour ${hour}`;
        }
      }

      if (day !== '*') {
        description += ` on day ${day}`;
      }

      if (month !== '*') {
        description += ` of month ${month}`;
      }

      if (weekday !== '*') {
        const weekdays = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
        if (weekdays[parseInt(weekday)]) {
          description += ` on ${weekdays[parseInt(weekday)]}`;
        }
      }

      return description;
    } catch (error) {
      return 'Invalid cron expression';
    }
  }

  /**
   * Gets the time until next execution
   * @param {string} cronExpression - Cron expression
   * @param {Date} currentTime - Current time (default: now)
   * @param {string} timezone - Timezone (default: UTC)
   * @returns {number|null} - Milliseconds until next execution or null if invalid
   */
  static getTimeUntilNext(cronExpression, currentTime = new Date(), timezone = 'UTC') {
    const nextTime = this.getNextRunTime(cronExpression, currentTime, timezone);
    if (!nextTime) {
      return null;
    }

    return nextTime.getTime() - currentTime.getTime();
  }

  /**
   * Checks if a cron expression represents a frequent schedule (< 1 hour)
   * @param {string} cronExpression - Cron expression
   * @returns {boolean} - Whether schedule is frequent
   */
  static isFrequentSchedule(cronExpression) {
    try {
      const now = new Date();
      const nextTimes = this.getNextRunTimes(cronExpression, 2, now);

      if (nextTimes.length < 2) {
        return false;
      }

      const interval = nextTimes[1].getTime() - nextTimes[0].getTime();
      return interval < 60 * 60 * 1000; // Less than 1 hour
    } catch (error) {
      return false;
    }
  }

  /**
   * Gets all tasks that should run within a time window
   * @param {Array} tasks - Array of task objects with cron expressions
   * @param {Date} startTime - Start of time window
   * @param {Date} endTime - End of time window
   * @returns {Array} - Tasks that should run in the window
   */
  static getTasksInWindow(tasks, startTime, endTime) {
    const tasksToRun = [];

    for (const task of tasks) {
      if (!task.cron || task.status !== 'active') {
        continue;
      }

      try {
        const interval = parser.parseExpression(task.cron, {
          currentDate: startTime,
          tz: task.timezone || 'UTC'
        });

        // Check if any execution time falls within the window
        let nextTime = interval.next().toDate();
        while (nextTime <= endTime) {
          if (nextTime >= startTime) {
            tasksToRun.push({
              ...task,
              scheduledTime: nextTime
            });
            break; // Only add once per window
          }
          nextTime = interval.next().toDate();
        }
      } catch (error) {
        console.error(`Error processing task ${task.id} cron:`, error);
      }
    }

    return tasksToRun;
  }

  /**
   * Calculates optimal cron trigger time for scheduler
   * @param {Array} tasks - Array of active tasks
   * @param {Object} options - Additional options for optimization
   * @returns {string} - Optimal cron expression for scheduler
   */
  static calculateOptimalSchedulerCron(tasks, options = {}) {
    const {
      systemLoad = 0,
      failureRate = 0,
      maxFrequency = '*/1 * * * *',
      minFrequency = '*/15 * * * *',
      considerSystemHealth = true
    } = options;

    if (!tasks || tasks.length === 0) {
      return '*/5 * * * *'; // Default: every 5 minutes
    }

    // Analyze task patterns
    const analysis = this.analyzeTaskPatterns(tasks);

    // Calculate base frequency based on task patterns
    let baseInterval = this.calculateBaseInterval(analysis);

    // Apply system health adjustments
    if (considerSystemHealth) {
      baseInterval = this.adjustForSystemHealth(baseInterval, systemLoad, failureRate);
    }

    // Apply task density adjustments
    baseInterval = this.adjustForTaskDensity(baseInterval, tasks.length, analysis);

    // Convert interval to cron expression
    const cronExpression = this.intervalToCronExpression(baseInterval);

    // Ensure within bounds
    return this.constrainCronFrequency(cronExpression, maxFrequency, minFrequency);
  }

  /**
   * Analyzes patterns in task schedules
   * @param {Array} tasks - Array of active tasks
   * @returns {Object} - Analysis results
   */
  static analyzeTaskPatterns(tasks) {
    const analysis = {
      intervals: [],
      frequencies: new Map(),
      patterns: {
        minutely: 0,
        hourly: 0,
        daily: 0,
        weekly: 0,
        custom: 0
      },
      minInterval: Infinity,
      maxInterval: 0,
      avgInterval: 0
    };

    for (const task of tasks) {
      if (!task.cron || task.status !== 'active') {
        continue;
      }

      try {
        const now = new Date();
        const nextTimes = this.getNextRunTimes(task.cron, 3, now);

        if (nextTimes.length >= 2) {
          const interval = nextTimes[1].getTime() - nextTimes[0].getTime();
          analysis.intervals.push(interval);
          analysis.minInterval = Math.min(analysis.minInterval, interval);
          analysis.maxInterval = Math.max(analysis.maxInterval, interval);

          // Categorize pattern
          this.categorizeTaskPattern(task.cron, analysis.patterns);

          // Track frequency
          const freq = Math.round(interval / 60000); // Convert to minutes
          analysis.frequencies.set(freq, (analysis.frequencies.get(freq) || 0) + 1);
        }
      } catch (error) {
        // Ignore invalid cron expressions
        console.warn(`Invalid cron expression in task ${task.id}: ${task.cron}`);
      }
    }

    // Calculate average interval
    if (analysis.intervals.length > 0) {
      analysis.avgInterval = analysis.intervals.reduce((sum, interval) => sum + interval, 0) / analysis.intervals.length;
    }

    return analysis;
  }

  /**
   * Categorizes a task pattern
   * @param {string} cronExpression - Cron expression
   * @param {Object} patterns - Patterns object to update
   */
  static categorizeTaskPattern(cronExpression, patterns) {
    const parts = cronExpression.split(' ');
    const [minute, hour, day, month, weekday] = parts;

    if (minute.includes('*') && hour === '*') {
      patterns.minutely++;
    } else if (hour.includes('*') && day === '*') {
      patterns.hourly++;
    } else if (day === '*' && month === '*' && weekday === '*') {
      patterns.daily++;
    } else if (weekday !== '*') {
      patterns.weekly++;
    } else {
      patterns.custom++;
    }
  }

  /**
   * Calculates base interval from task analysis
   * @param {Object} analysis - Task pattern analysis
   * @returns {number} - Base interval in milliseconds
   */
  static calculateBaseInterval(analysis) {
    if (analysis.intervals.length === 0) {
      return 5 * 60 * 1000; // 5 minutes default
    }

    // Use the most common frequency, but bias towards more frequent checking
    let optimalInterval = analysis.minInterval;

    // If we have multiple tasks, use a frequency that can catch most of them
    if (analysis.intervals.length > 1) {
      // Find the GCD-like optimal interval
      const sortedIntervals = [...analysis.intervals].sort((a, b) => a - b);

      // Use the smallest interval that's not too frequent
      for (const interval of sortedIntervals) {
        if (interval >= 60000) { // At least 1 minute
          optimalInterval = interval;
          break;
        }
      }

      // But don't go below the most frequent task's interval
      optimalInterval = Math.min(optimalInterval, analysis.minInterval * 2);
    }

    return Math.max(optimalInterval, 60000); // Minimum 1 minute
  }

  /**
   * Adjusts interval based on system health
   * @param {number} baseInterval - Base interval in milliseconds
   * @param {number} systemLoad - System load (0-1)
   * @param {number} failureRate - Failure rate (0-1)
   * @returns {number} - Adjusted interval
   */
  static adjustForSystemHealth(baseInterval, systemLoad, failureRate) {
    let adjustedInterval = baseInterval;

    // Increase interval if system is under load
    if (systemLoad > 0.7) {
      adjustedInterval *= 1.5;
    } else if (systemLoad > 0.5) {
      adjustedInterval *= 1.2;
    }

    // Increase interval if failure rate is high
    if (failureRate > 0.3) {
      adjustedInterval *= 1.4;
    } else if (failureRate > 0.1) {
      adjustedInterval *= 1.1;
    }

    return adjustedInterval;
  }

  /**
   * Adjusts interval based on task density
   * @param {number} baseInterval - Base interval in milliseconds
   * @param {number} taskCount - Number of active tasks
   * @param {Object} analysis - Task analysis
   * @returns {number} - Adjusted interval
   */
  static adjustForTaskDensity(baseInterval, taskCount, analysis) {
    let adjustedInterval = baseInterval;

    // More frequent checking for more tasks
    if (taskCount > 20) {
      adjustedInterval *= 0.8;
    } else if (taskCount > 10) {
      adjustedInterval *= 0.9;
    } else if (taskCount < 3) {
      adjustedInterval *= 1.2;
    }

    // Adjust based on pattern diversity
    const patternCount = Object.values(analysis.patterns).filter(count => count > 0).length;
    if (patternCount > 3) {
      adjustedInterval *= 0.9; // More diverse patterns need more frequent checking
    }

    return adjustedInterval;
  }

  /**
   * Converts interval to cron expression
   * @param {number} interval - Interval in milliseconds
   * @returns {string} - Cron expression
   */
  static intervalToCronExpression(interval) {
    const minutes = Math.round(interval / 60000);

    if (minutes <= 1) {
      return '*/1 * * * *';
    } else if (minutes <= 2) {
      return '*/2 * * * *';
    } else if (minutes <= 3) {
      return '*/3 * * * *';
    } else if (minutes <= 5) {
      return '*/5 * * * *';
    } else if (minutes <= 10) {
      return '*/10 * * * *';
    } else if (minutes <= 15) {
      return '*/15 * * * *';
    } else if (minutes <= 30) {
      return '*/30 * * * *';
    } else {
      return '0 */1 * * *'; // Every hour
    }
  }

  /**
   * Constrains cron frequency within bounds
   * @param {string} cronExpression - Proposed cron expression
   * @param {string} maxFrequency - Maximum frequency (most frequent allowed)
   * @param {string} minFrequency - Minimum frequency (least frequent allowed)
   * @returns {string} - Constrained cron expression
   */
  static constrainCronFrequency(cronExpression, maxFrequency, minFrequency) {
    const frequencies = [
      '*/1 * * * *',
      '*/2 * * * *',
      '*/3 * * * *',
      '*/5 * * * *',
      '*/10 * * * *',
      '*/15 * * * *',
      '*/30 * * * *',
      '0 */1 * * *'
    ];

    const currentIndex = frequencies.indexOf(cronExpression);
    const maxIndex = frequencies.indexOf(maxFrequency);
    const minIndex = frequencies.indexOf(minFrequency);

    if (currentIndex === -1) {
      return cronExpression; // Unknown expression, return as-is
    }

    if (maxIndex !== -1 && currentIndex < maxIndex) {
      return maxFrequency; // Too frequent
    }

    if (minIndex !== -1 && currentIndex > minIndex) {
      return minFrequency; // Too infrequent
    }

    return cronExpression;
  }

  /**
   * 计算精确匹配的触发器配置
   * @param {Array} activeTasks - 活跃任务列表
   * @param {Date} currentTime - 当前时间
   * @returns {Object} - 触发器配置结果
   */
  static calculateExactMatchTriggers(activeTasks, currentTime = new Date()) {
    const result = {
      triggers: ['*/5 * * * *', '*/5 * * * *', '0 */1 * * *'], // 默认配置
      nextTasks: [],
      strategy: 'exact_match',
      fallbackReason: null
    };

    try {
      // 1. 计算所有任务的下次执行时间
      const taskSchedules = [];

      for (const task of activeTasks) {
        if (!task.cron || task.status !== 'active') continue;

        const nextRunTime = this.getNextRunTime(task.cron, currentTime, task.timezone || 'UTC');
        if (nextRunTime) {
          taskSchedules.push({
            task,
            nextRunTime,
            cronExpression: task.cron,
            timezone: task.timezone || 'UTC'
          });
        }
      }

      // 2. 按执行时间排序
      taskSchedules.sort((a, b) => a.nextRunTime.getTime() - b.nextRunTime.getTime());

      // 3. 处理不同场景
      if (taskSchedules.length === 0) {
        // 场景1：没有活跃任务
        result.fallbackReason = 'no_active_tasks';
        return result;
      }

      if (taskSchedules.length === 1) {
        // 场景2：只有一个任务
        result.triggers[0] = this.convertToUTCCron(taskSchedules[0]);
        result.triggers[1] = '*/5 * * * *'; // 备用调度器保持默认
        result.nextTasks = [taskSchedules[0]];
        return result;
      }

      // 场景3：有多个任务，选择最近的两个
      const firstTask = taskSchedules[0];
      const secondTask = taskSchedules[1];

      // 4. 检查时间冲突
      const timeDiff = secondTask.nextRunTime.getTime() - firstTask.nextRunTime.getTime();
      const minInterval = 60000; // 最小间隔1分钟

      if (timeDiff < minInterval) {
        // 时间太接近，使用第一个任务和第三个任务（如果存在）
        if (taskSchedules.length > 2) {
          const thirdTask = taskSchedules[2];
          const thirdTimeDiff = thirdTask.nextRunTime.getTime() - firstTask.nextRunTime.getTime();

          if (thirdTimeDiff >= minInterval) {
            result.triggers[0] = this.convertToUTCCron(firstTask);
            result.triggers[1] = this.convertToUTCCron(thirdTask);
            result.nextTasks = [firstTask, thirdTask];
            return result;
          }
        }

        // 如果还是冲突，使用第一个任务 + 默认备用
        result.triggers[0] = this.convertToUTCCron(firstTask);
        result.triggers[1] = '*/5 * * * *';
        result.nextTasks = [firstTask];
        result.fallbackReason = 'time_conflict_resolved';
        return result;
      }

      // 5. 正常情况：使用最近的两个任务
      result.triggers[0] = this.convertToUTCCron(firstTask);
      result.triggers[1] = this.convertToUTCCron(secondTask);
      result.nextTasks = [firstTask, secondTask];

      return result;

    } catch (error) {
      console.error('Error calculating exact match triggers:', error);
      result.fallbackReason = `calculation_error: ${error.message}`;
      return result;
    }
  }

  /**
   * 将任务的 cron 表达式转换为 UTC 时区
   * @param {Object} taskSchedule - 任务调度信息
   * @returns {string} - UTC cron 表达式
   */
  static convertToUTCCron(taskSchedule) {
    const { task, nextRunTime, timezone } = taskSchedule;

    if (timezone === 'UTC') {
      return task.cron; // 已经是 UTC，直接返回
    }

    try {
      // 改进的时区处理：基于nextRunTime计算UTC时间的cron表达式
      // 这确保了触发器在正确的UTC时间触发

      const utcTime = new Date(nextRunTime.getTime());

      // 从UTC时间提取cron组件
      const minute = utcTime.getUTCMinutes();
      const hour = utcTime.getUTCHours();

      // 分析原始cron表达式的模式
      const cronParts = task.cron.trim().split(/\s+/);
      if (cronParts.length !== 5) {
        console.warn(`Invalid cron format for task ${task.name}: ${task.cron}, using original`);
        return task.cron;
      }

      // 构建UTC cron表达式，保持原有的模式但调整时间
      let utcCron = `${minute} ${hour}`;

      // 保持日期模式不变（简化处理）
      utcCron += ` ${cronParts[2]} ${cronParts[3]} ${cronParts[4]}`;

      console.log(`Task ${task.name}: ${timezone} cron ${task.cron} -> UTC cron ${utcCron}`);
      return utcCron;

    } catch (error) {
      console.error('Error converting cron to UTC:', error);
      console.log(`Fallback: using original cron ${task.cron} for task ${task.name}`);
      return task.cron; // 转换失败，返回原始表达式
    }
  }
}
