# Quick Start Guide

这是 Cron Task Manager 的快速启动指南。

## 🚀 快速开始

### 1. 环境要求

- Node.js 18+
- npm 或 pnpm
- Cloudflare 账户（用于部署）
- Wrangler CLI（`npm install -g wrangler`）

### 2. 本地开发

#### 安装依赖

```bash
# 安装所有依赖
.\scripts\dev.ps1 -Install

# 或者手动安装
cd worker && npm install
cd ../frontend && npm install
```

#### 启动开发服务器

```bash
# 启动完整开发环境（推荐）
.\scripts\dev.ps1

# 或者分别启动
.\scripts\dev.ps1 -FrontendOnly  # 仅前端
.\scripts\dev.ps1 -BackendOnly   # 仅后端
```

#### 访问应用

- 前端：http://localhost:5173
- 后端：http://localhost:8787
- 默认密码：`admin`（可在 worker/.dev.vars 中修改）

### 3. 功能测试

1. **登录系统**
   - 访问 http://localhost:5173
   - 输入密码 `admin`

2. **创建任务**
   - 进入 Tasks 页面
   - 点击 "Create Task"
   - 选择任务类型（如 HTTP Request）
   - 配置任务参数
   - 设置 Cron 表达式

3. **查看仪表盘**
   - 查看任务统计
   - 监控系统状态
   - 查看即将执行的任务

4. **查看日志**
   - 进入 Logs 页面
   - 查看执行日志
   - 筛选失败日志

### 4. 部署到生产环境

#### 配置 Cloudflare

1. 登录 Cloudflare：
   ```bash
   npx wrangler login
   ```

2. 创建 KV 命名空间：
   ```bash
   cd worker
   npx wrangler kv:namespace create "TASKS_KV"
   npx wrangler kv:namespace create "FAIL_LOGS_KV"
   ```

3. 更新 `worker/wrangler.toml` 中的 KV 命名空间 ID

4. 设置环境变量：
   ```bash
   npx wrangler secret put PASSWORD  --name cron-task-worker
   npx wrangler secret put JWT_SECRET  --name cron-task-worker
   npx wrangler secret put CLOUDFLARE_API_TOKEN  --name cron-task-worker
   npx wrangler secret put ACCOUNT_ID  --name cron-task-worker
   npx wrangler secret put WORKER_NAME  --name cron-task-worker
   ```

#### 部署应用

```bash
# 完整部署
.\scripts\deploy.ps1

# 或者分别部署
.\scripts\deploy.ps1 -BackendOnly   # 仅后端
.\scripts\deploy.ps1 -FrontendOnly  # 仅前端
```

### 5. 常见问题

#### Q: 前端无法连接后端
A: 检查 `frontend/.env` 中的 `VITE_API_BASE_URL` 是否正确

#### Q: 任务不执行
A: 检查任务状态是否为 "active"，Cron 表达式是否正确

#### Q: 登录失败
A: 检查密码是否正确，JWT_SECRET 是否设置

#### Q: Worker 部署失败
A: 检查 KV 命名空间是否创建，wrangler.toml 配置是否正确

### 6. 开发技巧

- 使用 `wrangler tail` 查看 Worker 实时日志
- 前端支持热重载，修改代码后自动刷新
- 可以在浏览器开发者工具中查看 API 请求
- 使用 Element Plus 的组件文档：https://element-plus.org/

### 7. 项目结构

```
cron-task/
├── worker/          # Cloudflare Worker 后端
├── frontend/        # Vue3 前端
├── scripts/         # 部署和开发脚本
├── docs/           # 文档
└── README.md       # 项目说明
```

### 8. 技术栈

**后端：**
- Cloudflare Workers
- Hono (Web 框架)
- Durable Objects (状态管理)
- KV Storage (数据存储)

**前端：**
- Vue 3 + Composition API
- Element Plus (UI 组件)
- Pinia (状态管理)
- TanStack Query (数据获取)
- Vite (构建工具)

### 9. 更多信息

- [完整文档](docs/)
- [API 文档](docs/API.md)
- [部署指南](docs/DEPLOYMENT.md)
- [项目 README](README.md)

---

如有问题，请查看文档或提交 Issue。
