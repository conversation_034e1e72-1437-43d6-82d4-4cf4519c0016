/**
 * Dashboard API routes
 */

import { Hono } from 'hono';
import { TaskService } from '../services/TaskService.js';
import { SchedulerService } from '../services/SchedulerService.js';
import { LogKV } from '../utils/kv.js';

const app = new Hono();

/**
 * GET /dashboard - Get dashboard data
 */
app.get('/', async (c) => {
  try {
    const taskService = new TaskService(c.env);
    const schedulerService = new SchedulerService(c.env);
    const logKV = new LogKV(c.env.FAIL_LOGS_KV);

    // Get all data in parallel
    const [
      taskStats,
      systemStatus,
      recentFailures,
      activeTasks
    ] = await Promise.all([
      taskService.getTaskStatistics(),
      schedulerService.getSystemStatus(),
      logKV.getFailureLogs(10),
      taskService.getActiveTasks()
    ]);

    // Calculate additional metrics
    const now = new Date();
    const upcomingTasks = activeTasks
      .filter(task => task.nextRunAt)
      .map(task => ({
        id: task.id,
        name: task.name,
        type: task.type,
        nextRunAt: task.nextRunAt,
        timeUntilRun: new Date(task.nextRunAt).getTime() - now.getTime()
      }))
      .sort((a, b) => a.timeUntilRun - b.timeUntilRun)
      .slice(0, 5);

    // Calculate failure rate (last 24 hours)
    const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);
    const recentFailuresCount = recentFailures.filter(log => 
      new Date(log.timestamp) > oneDayAgo
    ).length;

    // Calculate health score
    const healthScore = calculateHealthScore(taskStats, systemStatus, recentFailuresCount);

    // Prepare dashboard data
    const dashboardData = {
      overview: {
        totalTasks: taskStats.total,
        activeTasks: taskStats.active,
        inactiveTasks: taskStats.inactive,
        pausedTasks: taskStats.paused,
        healthScore: healthScore,
        lastUpdate: now.toISOString()
      },
      tasksByType: taskStats.byType,
      upcomingTasks: upcomingTasks,
      recentFailures: recentFailures.slice(0, 5).map(log => ({
        id: log.id,
        taskId: log.taskId,
        taskName: log.taskName,
        message: log.message,
        timestamp: log.timestamp,
        retryCount: log.retryCount
      })),
      systemStatus: {
        health: systemStatus.health,
        lastSchedulerRun: systemStatus.lastSchedulerRun,
        uptime: systemStatus.metrics.uptime || 0,
        schedulerRuns: systemStatus.metrics.schedulerRuns || 0
      },
      alerts: generateAlerts(taskStats, systemStatus, recentFailuresCount, upcomingTasks)
    };

    return c.json({
      success: true,
      data: dashboardData,
      timestamp: now.toISOString()
    });

  } catch (error) {
    console.error('Dashboard data error:', error);
    return c.json({
      error: 'Internal Server Error',
      message: 'Failed to retrieve dashboard data'
    }, 500);
  }
});

/**
 * GET /dashboard/stats - Get detailed statistics
 */
app.get('/stats', async (c) => {
  try {
    const taskService = new TaskService(c.env);
    const logKV = new LogKV(c.env.FAIL_LOGS_KV);

    // Get detailed statistics
    const [taskStats, allLogs] = await Promise.all([
      taskService.getTaskStatistics(),
      logKV.getRecentLogs(100)
    ]);

    // Analyze logs for trends
    const now = new Date();
    const timeRanges = {
      lastHour: now.getTime() - 60 * 60 * 1000,
      last6Hours: now.getTime() - 6 * 60 * 60 * 1000,
      last24Hours: now.getTime() - 24 * 60 * 60 * 1000,
      lastWeek: now.getTime() - 7 * 24 * 60 * 60 * 1000
    };

    const logStats = {
      lastHour: { total: 0, failures: 0, successes: 0 },
      last6Hours: { total: 0, failures: 0, successes: 0 },
      last24Hours: { total: 0, failures: 0, successes: 0 },
      lastWeek: { total: 0, failures: 0, successes: 0 }
    };

    // Analyze logs by time range
    for (const log of allLogs) {
      const logTime = new Date(log.timestamp).getTime();
      
      for (const [range, cutoff] of Object.entries(timeRanges)) {
        if (logTime > cutoff) {
          logStats[range].total++;
          if (log.status === 'failure' || log.status === 'timeout') {
            logStats[range].failures++;
          } else if (log.status === 'success') {
            logStats[range].successes++;
          }
        }
      }
    }

    // Calculate success rates
    const successRates = {};
    for (const range of Object.keys(logStats)) {
      const stats = logStats[range];
      successRates[range] = stats.total > 0 ? 
        ((stats.successes / stats.total) * 100).toFixed(2) : 0;
    }

    return c.json({
      success: true,
      data: {
        taskStats,
        logStats,
        successRates,
        timestamp: now.toISOString()
      }
    });

  } catch (error) {
    console.error('Dashboard stats error:', error);
    return c.json({
      error: 'Internal Server Error',
      message: 'Failed to retrieve statistics'
    }, 500);
  }
});

/**
 * Calculates overall system health score
 * @param {Object} taskStats - Task statistics
 * @param {Object} systemStatus - System status
 * @param {number} recentFailures - Number of recent failures
 * @returns {number} - Health score (0-100)
 */
function calculateHealthScore(taskStats, systemStatus, recentFailures) {
  let score = 100;

  // Deduct points for inactive tasks
  if (taskStats.total > 0) {
    const inactiveRatio = (taskStats.inactive + taskStats.paused) / taskStats.total;
    score -= inactiveRatio * 20; // Max 20 points deduction
  }

  // Deduct points for recent failures
  if (recentFailures > 0) {
    score -= Math.min(recentFailures * 5, 30); // Max 30 points deduction
  }

  // Deduct points for system health issues
  const healthChecks = systemStatus.health?.checks || {};
  const unhealthyChecks = Object.values(healthChecks).filter(status => status !== 'healthy').length;
  score -= unhealthyChecks * 15; // 15 points per unhealthy check

  // Deduct points if scheduler hasn't run recently
  if (systemStatus.lastSchedulerRun) {
    const lastRun = new Date(systemStatus.lastSchedulerRun);
    const timeSinceLastRun = Date.now() - lastRun.getTime();
    if (timeSinceLastRun > 10 * 60 * 1000) { // More than 10 minutes
      score -= 20;
    }
  } else {
    score -= 25; // No scheduler run recorded
  }

  return Math.max(0, Math.round(score));
}

/**
 * Generates system alerts
 * @param {Object} taskStats - Task statistics
 * @param {Object} systemStatus - System status
 * @param {number} recentFailures - Number of recent failures
 * @param {Array} upcomingTasks - Upcoming tasks
 * @returns {Array} - Array of alert objects
 */
function generateAlerts(taskStats, systemStatus, recentFailures, upcomingTasks) {
  const alerts = [];

  // High failure rate alert
  if (recentFailures > 5) {
    alerts.push({
      type: 'error',
      title: 'High Failure Rate',
      message: `${recentFailures} task failures in the last 24 hours`,
      timestamp: new Date().toISOString()
    });
  }

  // No active tasks alert
  if (taskStats.active === 0 && taskStats.total > 0) {
    alerts.push({
      type: 'warning',
      title: 'No Active Tasks',
      message: 'All tasks are currently inactive or paused',
      timestamp: new Date().toISOString()
    });
  }

  // Scheduler not running alert
  if (systemStatus.lastSchedulerRun) {
    const lastRun = new Date(systemStatus.lastSchedulerRun);
    const timeSinceLastRun = Date.now() - lastRun.getTime();
    if (timeSinceLastRun > 15 * 60 * 1000) { // More than 15 minutes
      alerts.push({
        type: 'error',
        title: 'Scheduler Not Running',
        message: `Last scheduler run was ${Math.round(timeSinceLastRun / 60000)} minutes ago`,
        timestamp: new Date().toISOString()
      });
    }
  }

  // System health alerts
  const healthChecks = systemStatus.health?.checks || {};
  for (const [check, status] of Object.entries(healthChecks)) {
    if (status !== 'healthy') {
      alerts.push({
        type: 'warning',
        title: 'System Health Issue',
        message: `${check} is ${status}`,
        timestamp: new Date().toISOString()
      });
    }
  }

  // Upcoming task alerts (tasks due in next 5 minutes)
  const soonTasks = upcomingTasks.filter(task => task.timeUntilRun < 5 * 60 * 1000 && task.timeUntilRun > 0);
  if (soonTasks.length > 0) {
    alerts.push({
      type: 'info',
      title: 'Tasks Due Soon',
      message: `${soonTasks.length} task(s) will run in the next 5 minutes`,
      timestamp: new Date().toISOString()
    });
  }

  return alerts;
}

export default app;
