/**
 * Task Service - Handles task-related operations
 */

import { TaskKV } from '../utils/kv.js';
import { CronUtil } from '../utils/cron.js';

export class TaskService {
  constructor(env) {
    this.env = env;
    this.taskKV = new TaskKV(env.TASKS_KV);
  }

  /**
   * Gets all tasks
   * @returns {Promise<Array>} - Array of task objects
   */
  async getAllTasks() {
    return await this.taskKV.getAllTasks();
  }

  /**
   * Gets active tasks
   * @returns {Promise<Array>} - Array of active task objects
   */
  async getActiveTasks() {
    return await this.taskKV.getActiveTasks();
  }

  /**
   * Gets a task by ID
   * @param {string} taskId - Task ID
   * @returns {Promise<Object|null>} - Task object or null
   */
  async getTask(taskId) {
    return await this.taskKV.getTask(taskId);
  }

  /**
   * Saves a task
   * @param {Object} task - Task object
   * @returns {Promise<boolean>} - Success status
   */
  async saveTask(task) {
    // Update timestamps
    task.updatedAt = new Date().toISOString();
    
    // Calculate next run time if task is active
    if (task.status === 'active' && task.cron) {
      const nextRunTime = CronUtil.getNextRunTime(task.cron, new Date(), task.timezone);
      if (nextRunTime) {
        task.nextRunAt = nextRunTime.toISOString();
      }
    } else {
      task.nextRunAt = null;
    }

    return await this.taskKV.saveTask(task);
  }

  /**
   * Deletes a task
   * @param {string} taskId - Task ID
   * @returns {Promise<boolean>} - Success status
   */
  async deleteTask(taskId) {
    return await this.taskKV.deleteTask(taskId);
  }

  /**
   * Gets tasks that should run at a specific time
   * @param {Date} scheduledTime - Time to check for tasks
   * @returns {Promise<Array>} - Array of tasks to run
   */
  async getTasksToRun(scheduledTime) {
    try {
      const activeTasks = await this.getActiveTasks();
      const tasksToRun = [];

      for (const task of activeTasks) {
        if (this.shouldTaskRun(task, scheduledTime)) {
          tasksToRun.push(task);
        }
      }

      return tasksToRun;
    } catch (error) {
      console.error('Error getting tasks to run:', error);
      return [];
    }
  }

  /**
   * Gets tasks that were missed in a time window
   * @param {Date} startTime - Start of time window
   * @param {Date} endTime - End of time window
   * @returns {Promise<Array>} - Array of missed tasks
   */
  async getMissedTasks(startTime, endTime) {
    try {
      const activeTasks = await this.getActiveTasks();
      const missedTasks = [];

      for (const task of activeTasks) {
        if (this.wasTaskMissed(task, startTime, endTime)) {
          missedTasks.push(task);
        }
      }

      return missedTasks;
    } catch (error) {
      console.error('Error getting missed tasks:', error);
      return [];
    }
  }

  /**
   * Checks if a task should run at a specific time
   * @param {Object} task - Task object
   * @param {Date} scheduledTime - Time to check
   * @returns {boolean} - Whether task should run
   */
  shouldTaskRun(task, scheduledTime) {
    if (!task.cron || task.status !== 'active') {
      return false;
    }

    try {
      // Check if the scheduled time matches the cron expression
      const shouldRun = CronUtil.shouldRunAt(task.cron, scheduledTime, task.timezone);
      
      if (!shouldRun) {
        return false;
      }

      // Additional check: ensure we don't run the same task multiple times
      // within a short time window (prevent duplicate executions)
      if (task.lastRunAt) {
        const lastRun = new Date(task.lastRunAt);
        const timeSinceLastRun = scheduledTime.getTime() - lastRun.getTime();
        
        // Don't run if last execution was less than 30 seconds ago
        if (timeSinceLastRun < 30000) {
          return false;
        }
      }

      return true;
    } catch (error) {
      console.error(`Error checking if task ${task.id} should run:`, error);
      return false;
    }
  }

  /**
   * Checks if a task was missed in a time window
   * @param {Object} task - Task object
   * @param {Date} startTime - Start of time window
   * @param {Date} endTime - End of time window
   * @returns {boolean} - Whether task was missed
   */
  wasTaskMissed(task, startTime, endTime) {
    if (!task.cron || task.status !== 'active') {
      return false;
    }

    try {
      // Get all scheduled times in the window
      const scheduledTimes = CronUtil.getTasksInWindow([task], startTime, endTime);
      
      if (scheduledTimes.length === 0) {
        return false;
      }

      // Check if any scheduled time was missed
      for (const scheduledTask of scheduledTimes) {
        const scheduledTime = scheduledTask.scheduledTime;
        
        // Check if this time was actually executed
        if (task.lastRunAt) {
          const lastRun = new Date(task.lastRunAt);
          const timeDiff = Math.abs(scheduledTime.getTime() - lastRun.getTime());
          
          // If last run was within 1 minute of scheduled time, consider it executed
          if (timeDiff < 60000) {
            continue;
          }
        }
        
        // This scheduled time was missed
        return true;
      }

      return false;
    } catch (error) {
      console.error(`Error checking if task ${task.id} was missed:`, error);
      return false;
    }
  }

  /**
   * Updates task's last run time
   * @param {string} taskId - Task ID
   * @param {Date} timestamp - Last run timestamp
   * @returns {Promise<boolean>} - Success status
   */
  async updateLastRunTime(taskId, timestamp) {
    return await this.taskKV.updateLastRunTime(taskId, timestamp);
  }

  /**
   * Updates task's next run time
   * @param {string} taskId - Task ID
   * @param {Date} timestamp - Next run timestamp
   * @returns {Promise<boolean>} - Success status
   */
  async updateNextRunTime(taskId, timestamp) {
    return await this.taskKV.updateNextRunTime(taskId, timestamp);
  }

  /**
   * Gets task statistics
   * @returns {Promise<Object>} - Task statistics
   */
  async getTaskStatistics() {
    try {
      const allTasks = await this.getAllTasks();
      
      const stats = {
        total: allTasks.length,
        active: 0,
        inactive: 0,
        paused: 0,
        byType: {},
        nextRuns: []
      };

      for (const task of allTasks) {
        // Count by status
        stats[task.status] = (stats[task.status] || 0) + 1;
        
        // Count by type
        stats.byType[task.type] = (stats.byType[task.type] || 0) + 1;
        
        // Collect next run times for active tasks
        if (task.status === 'active' && task.nextRunAt) {
          stats.nextRuns.push({
            taskId: task.id,
            taskName: task.name,
            nextRunAt: task.nextRunAt
          });
        }
      }

      // Sort next runs by time
      stats.nextRuns.sort((a, b) => new Date(a.nextRunAt) - new Date(b.nextRunAt));
      
      // Keep only next 10 runs
      stats.nextRuns = stats.nextRuns.slice(0, 10);

      return stats;
    } catch (error) {
      console.error('Error getting task statistics:', error);
      return {
        total: 0,
        active: 0,
        inactive: 0,
        paused: 0,
        byType: {},
        nextRuns: []
      };
    }
  }

  /**
   * Validates task configuration
   * @param {Object} task - Task object to validate
   * @returns {Object} - Validation result
   */
  validateTask(task) {
    const errors = [];

    // Basic validation
    if (!task.name || task.name.trim().length === 0) {
      errors.push('Task name is required');
    }

    if (!task.type) {
      errors.push('Task type is required');
    }

    if (!task.cron) {
      errors.push('Cron expression is required');
    } else {
      const cronValidation = CronUtil.validateCronExpression(task.cron);
      if (!cronValidation.valid) {
        errors.push(`Invalid cron expression: ${cronValidation.error}`);
      }
    }

    if (!task.config || typeof task.config !== 'object') {
      errors.push('Task configuration is required');
    }

    // Validate retry configuration
    if (task.retryConfig && task.retryConfig.enabled) {
      if (task.retryConfig.maxRetries < 0 || task.retryConfig.maxRetries > 10) {
        errors.push('Max retries must be between 0 and 10');
      }
      
      if (task.retryConfig.baseDelay < 1000 || task.retryConfig.baseDelay > 300000) {
        errors.push('Base delay must be between 1000ms and 300000ms');
      }
    }

    // Validate timeout
    if (task.timeout && (task.timeout < 1000 || task.timeout > 300000)) {
      errors.push('Timeout must be between 1000ms and 300000ms');
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }
}
