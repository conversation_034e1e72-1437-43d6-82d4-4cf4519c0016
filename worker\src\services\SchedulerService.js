/**
 * Scheduler Service - Handles scheduling and system maintenance
 */

import { GeneralKV, LogKV } from '../utils/kv.js';
import { CronUtil } from '../utils/cron.js';
import { createSystemLog } from '../schemas/log.js';
import { CloudflareAPIService } from './CloudflareAPIService.js';
import { validateCronUpdateOperation } from '../utils/validation.js';

export class SchedulerService {
  constructor(env) {
    this.env = env;
    this.generalKV = new GeneralKV(env.TASKS_KV);
    this.logKV = new LogKV(env.FAIL_LOGS_KV);
    this.cloudflareAPI = new CloudflareAPIService(env);
  }

  /**
   * Updates the next cron trigger time for dynamic scheduling
   * @param {Date} currentTime - Current scheduled time
   * @param {Array} activeTasks - Array of active tasks (optional)
   * @returns {Promise<Object>} - Update result
   */
  async updateNextCronTrigger(currentTime, activeTasks = null) {
    // 检查是否启用精确匹配策略
    const useExactMatch = this.env.CRON_STRATEGY === 'exact_match';

    if (useExactMatch) {
      return await this.updateTriggersWithExactMatch(currentTime, activeTasks);
    } else {
      return await this.updateTriggersWithFrequencyOptimization(currentTime, activeTasks);
    }
  }

  /**
   * 使用精确匹配策略更新触发器
   * @param {Date} currentTime - 当前时间
   * @param {Array} activeTasks - 活跃任务列表
   * @returns {Promise<Object>} - 更新结果
   */
  async updateTriggersWithExactMatch(currentTime, activeTasks = null) {
    const result = {
      success: false,
      message: '',
      strategy: 'exact_match',
      triggersUpdated: false,
      nextTasks: [],
      fallbackReason: null,
      cronUpdated: false,
      skipped: false
    };

    try {
      console.log('Starting exact match cron trigger update...');

      // Store the last scheduler run time
      await this.generalKV.set('last_scheduler_run', currentTime.toISOString());

      // Check if dynamic cron updates are enabled
      const dynamicCronEnabled = this.env.ENABLE_DYNAMIC_CRON === 'true';
      if (!dynamicCronEnabled) {
        result.message = 'Dynamic cron updates are disabled';
        result.skipped = true;
        result.success = true;
        console.log('Dynamic cron updates disabled, skipping...');
        return result;
      }

      // Check if Cloudflare API is configured
      if (!this.cloudflareAPI.isConfigured()) {
        result.message = 'Cloudflare API not configured';
        result.skipped = true;
        result.success = true;
        console.log('Cloudflare API not configured, skipping cron update...');
        return result;
      }

      // Get active tasks if not provided
      if (!activeTasks) {
        const { TaskService } = await import('./TaskService.js');
        const taskService = new TaskService(this.env);
        activeTasks = await taskService.getActiveTasks();
      }

      // 1. 计算精确匹配的触发器
      const triggerConfig = CronUtil.calculateExactMatchTriggers(activeTasks, currentTime);

      result.nextTasks = triggerConfig.nextTasks;
      result.fallbackReason = triggerConfig.fallbackReason;

      console.log('Calculated exact match triggers:', {
        triggers: triggerConfig.triggers,
        nextTasks: triggerConfig.nextTasks.map(t => ({
          name: t.task.name,
          cron: t.cronExpression,
          nextRun: t.nextRunTime.toISOString()
        })),
        fallbackReason: triggerConfig.fallbackReason
      });

      // 2. 检查是否需要更新
      const currentTriggers = await this.cloudflareAPI.getCurrentCronTriggers();
      const newTriggers = triggerConfig.triggers;

      // 比较前两个触发器（看门狗保持不变）
      const needsUpdate =
        currentTriggers[0] !== newTriggers[0] ||
        currentTriggers[1] !== newTriggers[1];

      if (!needsUpdate) {
        result.success = true;
        result.message = 'Triggers already match next tasks';
        result.skipped = true;
        console.log('Current triggers already match next tasks, no update needed');
        return result;
      }

      // 3. 验证更新操作
      const systemHealth = await this.calculateSystemLoad();
      const lastUpdateTime = await this.cloudflareAPI.getLastUpdateTime();

      const validation = validateCronUpdateOperation({
        env: this.env,
        triggers: newTriggers,
        tasks: activeTasks,
        systemHealth,
        lastUpdateTime: lastUpdateTime?.getTime(),
        force: false
      });

      if (!validation.canProceed) {
        result.message = `Update validation failed: ${validation.errors.join(', ')}`;
        result.skipped = true;
        result.success = false;
        console.log('Exact match cron update validation failed:', validation.summary);
        return result;
      }

      if (validation.warnings.length > 0) {
        console.warn('Exact match cron update warnings:', validation.warnings);
      }

      // 4. 执行更新
      const updateResult = await this.cloudflareAPI.safeCronUpdate(
        newTriggers,
        `exact_match_${triggerConfig.nextTasks.length}_tasks`
      );

      result.success = updateResult.success;
      result.message = updateResult.message;
      result.triggersUpdated = updateResult.success && !updateResult.skipped;
      result.cronUpdated = result.triggersUpdated;
      result.skipped = updateResult.skipped;

      if (result.triggersUpdated) {
        console.log(`Triggers updated for exact match:`, {
          task1: triggerConfig.nextTasks[0]?.task?.name,
          cron1: newTriggers[0],
          nextRun1: triggerConfig.nextTasks[0]?.nextRunTime?.toISOString(),
          task2: triggerConfig.nextTasks[1]?.task?.name,
          cron2: newTriggers[1],
          nextRun2: triggerConfig.nextTasks[1]?.nextRunTime?.toISOString()
        });

        // Log the successful update
        const updateLog = createSystemLog(
          `Exact match triggers updated: ${newTriggers[0]} | ${newTriggers[1]} (${triggerConfig.nextTasks.length} next tasks)`,
          'info',
          {
            strategy: 'exact_match',
            oldTriggers: [currentTriggers[0], currentTriggers[1]],
            newTriggers: [newTriggers[0], newTriggers[1]],
            nextTasks: triggerConfig.nextTasks.map(t => ({
              name: t.task.name,
              cron: t.cronExpression,
              nextRun: t.nextRunTime.toISOString()
            })),
            reason: 'exact_match_optimization'
          }
        );

        // 存储系统日志到KV
        await this.logKV.addLog(updateLog);

        // Store update metrics
        await this.recordCronUpdateMetrics(currentTime, currentTriggers[0], newTriggers[0], activeTasks.length);
      } else {
        console.log(`Exact match trigger update ${result.skipped ? 'skipped' : 'failed'}: ${result.message}`);
      }

      return result;
    } catch (error) {
      result.message = `Exact match update failed: ${error.message}`;
      console.error('Exact match trigger update error:', error);

      // Log the error
      const errorLog = createSystemLog(
        `Exact match trigger update failed: ${error.message}`,
        'error',
        { error: error.message, timestamp: currentTime.toISOString() }
      );

      // 存储错误日志到KV
      await this.logKV.addLog(errorLog);

      return result;
    }
  }

  /**
   * 使用频率优化策略更新触发器（原有逻辑）
   * @param {Date} currentTime - 当前时间
   * @param {Array} activeTasks - 活跃任务列表
   * @returns {Promise<Object>} - 更新结果
   */
  async updateTriggersWithFrequencyOptimization(currentTime, activeTasks = null) {
    const result = {
      success: false,
      message: '',
      cronUpdated: false,
      optimalCron: null,
      skipped: false
    };

    try {
      console.log('Starting frequency optimization cron trigger update...');

      // Store the last scheduler run time
      await this.generalKV.set('last_scheduler_run', currentTime.toISOString());

      // Check if dynamic cron updates are enabled
      const dynamicCronEnabled = this.env.ENABLE_DYNAMIC_CRON === 'true';
      if (!dynamicCronEnabled) {
        result.message = 'Dynamic cron updates are disabled';
        result.skipped = true;
        result.success = true;
        console.log('Dynamic cron updates disabled, skipping...');
        return result;
      }

      // Check if Cloudflare API is configured
      if (!this.cloudflareAPI.isConfigured()) {
        result.message = 'Cloudflare API not configured';
        result.skipped = true;
        result.success = true;
        console.log('Cloudflare API not configured, skipping cron update...');
        return result;
      }

      // Get active tasks if not provided
      if (!activeTasks) {
        const { TaskService } = await import('./TaskService.js');
        const taskService = new TaskService(this.env);
        activeTasks = await taskService.getActiveTasks();
      }

      // Get system health metrics for intelligent scheduling
      const systemLoad = await this.calculateSystemLoad();

      // Calculate optimal cron expression based on active tasks and system health
      const optimalCron = CronUtil.calculateOptimalSchedulerCron(activeTasks, {
        systemLoad: systemLoad.failureRate || 0,
        failureRate: systemLoad.failureRate || 0,
        considerSystemHealth: true
      });
      result.optimalCron = optimalCron;

      console.log(`Calculated optimal cron expression: ${optimalCron}`);

      // Get current triggers to compare
      const currentTriggers = await this.cloudflareAPI.getCurrentCronTriggers();
      const mainTrigger = currentTriggers[0] || '*/5 * * * *';

      // Check if the optimal cron is different from current main trigger
      if (mainTrigger === optimalCron) {
        result.message = 'Current cron trigger is already optimal';
        result.success = true;
        result.skipped = true;
        console.log('Current cron trigger is already optimal, no update needed');
        return result;
      }

      // Prepare new trigger array (keep backup triggers)
      const newTriggers = [
        optimalCron,           // Main dynamic scheduler
        '*/5 * * * *',         // Backup scheduler
        '0 */1 * * *'          // Watchdog
      ];

      // Validate the update operation
      const systemHealth = await this.calculateSystemLoad();
      const lastUpdateTime = await this.cloudflareAPI.getLastUpdateTime();

      const validation = validateCronUpdateOperation({
        env: this.env,
        triggers: newTriggers,
        tasks: activeTasks,
        systemHealth,
        lastUpdateTime: lastUpdateTime?.getTime(),
        force: false
      });

      if (!validation.canProceed) {
        result.message = `Update validation failed: ${validation.errors.join(', ')}`;
        result.skipped = true;
        result.success = false;
        console.log('Cron update validation failed:', validation.summary);
        return result;
      }

      if (validation.warnings.length > 0) {
        console.warn('Cron update warnings:', validation.warnings);
      }

      // Perform safe cron update
      const updateResult = await this.cloudflareAPI.safeCronUpdate(
        newTriggers,
        `automatic_optimization_${activeTasks.length}_tasks`
      );

      result.success = updateResult.success;
      result.message = updateResult.message;
      result.cronUpdated = updateResult.success && !updateResult.skipped;
      result.skipped = updateResult.skipped;

      if (result.cronUpdated) {
        console.log(`Successfully updated cron trigger from ${mainTrigger} to ${optimalCron}`);

        // Log the successful update
        const updateLog = createSystemLog(
          `Cron trigger updated: ${mainTrigger} → ${optimalCron} (${activeTasks.length} active tasks)`,
          'info',
          {
            oldTrigger: mainTrigger,
            newTrigger: optimalCron,
            activeTaskCount: activeTasks.length,
            reason: 'automatic_optimization'
          }
        );

        // 存储系统日志到KV
        await this.logKV.addLog(updateLog);

        // Store update metrics
        await this.recordCronUpdateMetrics(currentTime, mainTrigger, optimalCron, activeTasks.length);
      } else {
        console.log(`Cron trigger update ${result.skipped ? 'skipped' : 'failed'}: ${result.message}`);
      }

      return result;
    } catch (error) {
      result.message = `Error updating cron trigger: ${error.message}`;
      console.error('Error updating cron trigger:', error);

      // Log the error
      const errorLog = createSystemLog(
        `Cron trigger update failed: ${error.message}`,
        'error',
        { error: error.message, timestamp: currentTime.toISOString() }
      );

      // 存储错误日志到KV
      await this.logKV.addLog(errorLog);

      return result;
    }
  }

  /**
   * Records cron update metrics for monitoring and analysis
   * @param {Date} timestamp - Update timestamp
   * @param {string} oldTrigger - Previous cron trigger
   * @param {string} newTrigger - New cron trigger
   * @param {number} activeTaskCount - Number of active tasks
   * @returns {Promise<void>}
   */
  async recordCronUpdateMetrics(timestamp, oldTrigger, newTrigger, activeTaskCount) {
    try {
      const metrics = {
        timestamp: timestamp.toISOString(),
        oldTrigger,
        newTrigger,
        activeTaskCount,
        updateReason: 'automatic_optimization',
        systemLoad: await this.calculateSystemLoad(),
        id: crypto.randomUUID()
      };

      // Store in metrics history
      const metricsKey = `cron_metrics_${timestamp.getFullYear()}_${timestamp.getMonth()}_${timestamp.getDate()}`;
      const dailyMetrics = await this.generalKV.get(metricsKey, []);

      dailyMetrics.push(metrics);

      // Keep only last 100 entries per day
      if (dailyMetrics.length > 100) {
        dailyMetrics.splice(0, dailyMetrics.length - 100);
      }

      await this.generalKV.set(metricsKey, dailyMetrics, { expirationTtl: 7 * 24 * 60 * 60 }); // 7 days TTL

      // Update latest metrics
      await this.generalKV.set('latest_cron_update_metrics', metrics);

      console.log('Recorded cron update metrics:', metrics);
    } catch (error) {
      console.error('Error recording cron update metrics:', error);
    }
  }

  /**
   * Calculates current system load based on various factors
   * @returns {Promise<Object>} - System load metrics
   */
  async calculateSystemLoad() {
    try {
      const [schedulerRuns, failureRate, lastHealthCheck] = await Promise.all([
        this.getSchedulerRunCount(),
        this.calculateRecentFailureRate(),
        this.generalKV.get('system_health')
      ]);

      return {
        schedulerRuns,
        failureRate,
        healthStatus: lastHealthCheck?.checks || {},
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      console.error('Error calculating system load:', error);
      return {
        schedulerRuns: 0,
        failureRate: 0,
        healthStatus: {},
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * Calculates recent failure rate for system load assessment
   * @returns {Promise<number>} - Failure rate (0-1)
   */
  async calculateRecentFailureRate() {
    try {
      const recentLogs = await this.logKV.getFailureLogs(20);
      if (recentLogs.length === 0) {
        return 0;
      }

      // Calculate failure rate in last hour
      const oneHourAgo = Date.now() - (60 * 60 * 1000);
      const recentFailures = recentLogs.filter(log =>
        new Date(log.timestamp).getTime() > oneHourAgo
      );

      return Math.min(recentFailures.length / 20, 1); // Normalize to 0-1
    } catch (error) {
      console.error('Error calculating failure rate:', error);
      return 0;
    }
  }

  /**
   * Performs system health checks
   * @returns {Promise<void>}
   */
  async performHealthCheck() {
    try {
      console.log('Performing system health check...');

      const healthData = {
        timestamp: new Date().toISOString(),
        checks: {}
      };

      // Check KV storage connectivity
      try {
        await this.generalKV.set('health_check', Date.now().toString());
        const value = await this.generalKV.get('health_check');
        healthData.checks.kv_storage = value ? 'healthy' : 'unhealthy';
      } catch (error) {
        healthData.checks.kv_storage = 'unhealthy';
        console.error('KV storage health check failed:', error);
      }

      // Check Durable Objects (basic test)
      try {
        // This would require a test DO call in production
        healthData.checks.durable_objects = 'healthy';
      } catch (error) {
        healthData.checks.durable_objects = 'unhealthy';
        console.error('Durable Objects health check failed:', error);
      }

      // Store health check results
      await this.generalKV.set('system_health', healthData);

      // Log system health
      const healthLog = createSystemLog(
        `System health check completed: ${JSON.stringify(healthData.checks)}`,
        'info',
        healthData
      );

      // 存储健康检查日志到KV
      await this.logKV.addLog(healthLog);

      console.log('System health check completed:', healthData.checks);

    } catch (error) {
      console.error('Health check failed:', error);

      const errorLog = createSystemLog(
        `System health check failed: ${error.message}`,
        'error',
        { error: error.message }
      );

      // 存储错误日志到KV
      await this.logKV.addLog(errorLog);
    }
  }

  /**
   * Cleans up old logs and performs maintenance
   * @returns {Promise<void>}
   */
  async cleanupOldLogs() {
    try {
      console.log('Performing log cleanup...');

      // The LogKV already implements a circular buffer, so no cleanup needed
      // But we can perform other maintenance tasks here

      // Clean up old health check data
      const oldHealthKeys = await this.generalKV.list('health_check_');
      const cutoffTime = Date.now() - (7 * 24 * 60 * 60 * 1000); // 7 days ago

      for (const key of oldHealthKeys) {
        try {
          const timestamp = parseInt(key.name.split('_').pop());
          if (timestamp && timestamp < cutoffTime) {
            await this.generalKV.delete(key.name);
          }
        } catch (error) {
          // Ignore individual key errors
        }
      }

      // Clean up old scheduler metadata
      await this.cleanupSchedulerMetadata();

      console.log('Log cleanup completed');

    } catch (error) {
      console.error('Log cleanup failed:', error);
    }
  }

  /**
   * Updates system metrics
   * @param {Date} timestamp - Current timestamp
   * @returns {Promise<void>}
   */
  async updateSystemMetrics(timestamp) {
    try {
      console.log('Updating system metrics...');

      const metrics = {
        timestamp: timestamp.toISOString(),
        schedulerRuns: await this.getSchedulerRunCount(),
        lastHealthCheck: await this.generalKV.get('system_health'),
        uptime: await this.calculateUptime(),
        memoryUsage: this.getMemoryUsage()
      };

      // Store metrics
      await this.generalKV.set('system_metrics', metrics);

      // Store historical metrics (keep last 24 hours)
      const hourKey = `metrics_${timestamp.getFullYear()}_${timestamp.getMonth()}_${timestamp.getDate()}_${timestamp.getHours()}`;
      await this.generalKV.set(hourKey, metrics, { expirationTtl: 24 * 60 * 60 }); // 24 hours TTL

      console.log('System metrics updated');

    } catch (error) {
      console.error('Error updating system metrics:', error);
    }
  }

  /**
   * Gets the scheduler run count
   * @returns {Promise<number>} - Number of scheduler runs
   */
  async getSchedulerRunCount() {
    try {
      const count = await this.generalKV.get('scheduler_run_count', 0);
      const newCount = (typeof count === 'number' ? count : 0) + 1;
      await this.generalKV.set('scheduler_run_count', newCount);
      return newCount;
    } catch (error) {
      console.error('Error getting scheduler run count:', error);
      return 0;
    }
  }

  /**
   * Calculates system uptime
   * @returns {Promise<number>} - Uptime in milliseconds
   */
  async calculateUptime() {
    try {
      const startTime = await this.generalKV.get('system_start_time');
      if (!startTime) {
        const now = Date.now();
        await this.generalKV.set('system_start_time', now);
        return 0;
      }

      return Date.now() - startTime;
    } catch (error) {
      console.error('Error calculating uptime:', error);
      return 0;
    }
  }

  /**
   * Gets memory usage information
   * @returns {Object} - Memory usage data
   */
  getMemoryUsage() {
    // In Cloudflare Workers, we don't have access to detailed memory info
    // This is a placeholder for potential future metrics
    return {
      used: 0,
      available: 0,
      percentage: 0
    };
  }

  /**
   * Cleans up old scheduler metadata
   * @returns {Promise<void>}
   */
  async cleanupSchedulerMetadata() {
    try {
      // Clean up old metrics (keep only last 7 days)
      const cutoffTime = Date.now() - (7 * 24 * 60 * 60 * 1000);
      const cutoffDate = new Date(cutoffTime);

      const metricsKeys = await this.generalKV.list('metrics_');

      for (const key of metricsKeys) {
        try {
          // Parse date from key name (metrics_YYYY_M_D_H)
          const parts = key.name.split('_');
          if (parts.length >= 5) {
            const year = parseInt(parts[1]);
            const month = parseInt(parts[2]);
            const day = parseInt(parts[3]);
            const hour = parseInt(parts[4]);

            const keyDate = new Date(year, month, day, hour);

            if (keyDate < cutoffDate) {
              await this.generalKV.delete(key.name);
            }
          }
        } catch (error) {
          // Ignore individual key errors
        }
      }

    } catch (error) {
      console.error('Error cleaning up scheduler metadata:', error);
    }
  }

  /**
   * Gets system status for dashboard
   * @returns {Promise<Object>} - System status
   */
  async getSystemStatus() {
    try {
      const [health, metrics, lastRun] = await Promise.all([
        this.generalKV.get('system_health'),
        this.generalKV.get('system_metrics'),
        this.generalKV.get('last_scheduler_run')
      ]);

      return {
        health: health || { checks: {} },
        metrics: metrics || {},
        lastSchedulerRun: lastRun,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      console.error('Error getting system status:', error);
      return {
        health: { checks: {} },
        metrics: {},
        lastSchedulerRun: null,
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * Records scheduler execution
   * @param {string} type - Scheduler type (main, backup, watchdog)
   * @param {Date} startTime - Start time
   * @param {Date} endTime - End time
   * @param {Object} result - Execution result
   * @returns {Promise<void>}
   */
  async recordSchedulerExecution(type, startTime, endTime, result) {
    try {
      const execution = {
        type,
        startTime: startTime.toISOString(),
        endTime: endTime.toISOString(),
        duration: endTime.getTime() - startTime.getTime(),
        result,
        timestamp: new Date().toISOString()
      };

      // Store latest execution
      await this.generalKV.set(`scheduler_execution_${type}`, execution);

      // Log execution
      const log = createSystemLog(
        `Scheduler ${type} executed in ${execution.duration}ms`,
        'info',
        execution
      );

      // 存储调度器执行日志到KV
      await this.logKV.addLog(log);

    } catch (error) {
      console.error('Error recording scheduler execution:', error);
    }
  }

  /**
   * Gets comprehensive cron trigger status and metrics
   * @returns {Promise<Object>} - Cron status information
   */
  async getCronTriggerStatus() {
    try {
      const [
        currentTriggers,
        updateHistory,
        lastUpdateTime,
        canUpdate,
        latestMetrics
      ] = await Promise.all([
        this.cloudflareAPI.getCurrentCronTriggers(),
        this.cloudflareAPI.getCronUpdateHistory(),
        this.cloudflareAPI.getLastUpdateTime(),
        this.cloudflareAPI.canUpdateNow(),
        this.generalKV.get('latest_cron_update_metrics')
      ]);

      // 获取当前策略
      const strategy = this.env.CRON_STRATEGY || 'frequency_optimization';

      // 如果是精确匹配策略，获取下一个任务信息
      let nextTasks = [];
      if (strategy === 'exact_match') {
        try {
          const { TaskService } = await import('./TaskService.js');
          const taskService = new TaskService(this.env);
          const activeTasks = await taskService.getActiveTasks();

          const triggerConfig = CronUtil.calculateExactMatchTriggers(activeTasks, new Date());
          nextTasks = triggerConfig.nextTasks;
        } catch (error) {
          console.error('Error getting next tasks for exact match:', error);
        }
      }

      return {
        currentTriggers,
        isConfigured: this.cloudflareAPI.isConfigured(),
        dynamicUpdatesEnabled: this.env.ENABLE_DYNAMIC_CRON === 'true',
        strategy, // 添加策略字段
        nextTasks, // 添加下一个任务信息
        lastUpdateTime: lastUpdateTime?.toISOString() || null,
        canUpdateNow: canUpdate,
        updateHistory: updateHistory.slice(0, 10), // Last 10 updates
        latestMetrics,
        nextUpdateAllowedAt: await this.getNextUpdateAllowedTime(),
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      console.error('Error getting cron trigger status:', error);
      return {
        currentTriggers: [],
        isConfigured: false,
        dynamicUpdatesEnabled: false,
        strategy: this.env.CRON_STRATEGY || 'frequency_optimization',
        nextTasks: [],
        lastUpdateTime: null,
        canUpdateNow: false,
        updateHistory: [],
        latestMetrics: null,
        nextUpdateAllowedAt: null,
        timestamp: new Date().toISOString(),
        error: error.message
      };
    }
  }

  /**
   * Gets the next time when cron update will be allowed
   * @returns {Promise<Date|null>} - Next allowed update time
   */
  async getNextUpdateAllowedTime() {
    try {
      const lastUpdate = await this.cloudflareAPI.getLastUpdateTime();
      if (!lastUpdate) {
        return null; // Can update immediately
      }

      const minInterval = parseInt(this.env.CRON_UPDATE_MIN_INTERVAL) || 300000; // 5 minutes default
      return new Date(lastUpdate.getTime() + minInterval);
    } catch (error) {
      console.error('Error calculating next update time:', error);
      return null;
    }
  }

  /**
   * Manually triggers a cron update with specified triggers
   * @param {Array} newTriggers - New cron triggers
   * @param {string} reason - Reason for manual update
   * @returns {Promise<Object>} - Update result
   */
  async manualCronUpdate(newTriggers, reason = 'manual') {
    try {
      console.log('Manual cron update requested:', newTriggers);

      const result = await this.cloudflareAPI.safeCronUpdate(newTriggers, reason);

      if (result.success && !result.skipped) {
        // Record manual update metrics
        await this.recordCronUpdateMetrics(
          new Date(),
          result.oldTriggers[0] || 'unknown',
          newTriggers[0] || 'unknown',
          0 // Manual updates don't have task count context
        );
      }

      return result;
    } catch (error) {
      console.error('Manual cron update failed:', error);
      return {
        success: false,
        message: `Manual update failed: ${error.message}`,
        skipped: false
      };
    }
  }

  /**
   * Forces a cron optimization based on current active tasks
   * @returns {Promise<Object>} - Optimization result
   */
  async forceCronOptimization() {
    try {
      console.log('Forcing cron optimization...');

      // Get current active tasks
      const { TaskService } = await import('./TaskService.js');
      const taskService = new TaskService(this.env);
      const activeTasks = await taskService.getActiveTasks();

      // Calculate optimal cron
      const optimalCron = CronUtil.calculateOptimalSchedulerCron(activeTasks);

      // Prepare new triggers
      const newTriggers = [
        optimalCron,           // Main dynamic scheduler
        '*/5 * * * *',         // Backup scheduler
        '0 */1 * * *'          // Watchdog
      ];

      // Force update (bypass rate limiting for manual optimization)
      const result = await this.cloudflareAPI.updateCronTriggers(newTriggers);

      if (result) {
        // Record the forced optimization
        await this.cloudflareAPI.recordCronUpdate(
          [], // Don't have old triggers in this context
          newTriggers,
          true,
          `forced_optimization_${activeTasks.length}_tasks`
        );

        await this.recordCronUpdateMetrics(
          new Date(),
          'unknown',
          optimalCron,
          activeTasks.length
        );
      }

      return {
        success: result,
        message: result ? 'Cron optimization completed successfully' : 'Cron optimization failed',
        optimalCron,
        activeTaskCount: activeTasks.length,
        newTriggers
      };
    } catch (error) {
      console.error('Force cron optimization failed:', error);
      return {
        success: false,
        message: `Optimization failed: ${error.message}`,
        optimalCron: null,
        activeTaskCount: 0,
        newTriggers: []
      };
    }
  }
}
