import { define<PERSON><PERSON> } from 'pinia'
import { tasksApi } from '@/api/tasks'
import { ElMessage, ElMessageBox } from 'element-plus'

export const useTasksStore = defineStore('tasks', {
  state: () => ({
    tasks: [],
    taskTypes: [],
    selectedTask: null,
    loading: false,
    error: null,
    filters: {
      status: '',
      type: '',
      search: ''
    },
    pagination: {
      page: 1,
      pageSize: 20,
      total: 0
    }
  }),

  getters: {
    filteredTasks: (state) => {
      let filtered = [...state.tasks]

      // Apply status filter
      if (state.filters.status) {
        filtered = filtered.filter(task => task.status === state.filters.status)
      }

      // Apply type filter
      if (state.filters.type) {
        filtered = filtered.filter(task => task.type === state.filters.type)
      }

      // Apply search filter
      if (state.filters.search) {
        const search = state.filters.search.toLowerCase()
        filtered = filtered.filter(task =>
          task.name.toLowerCase().includes(search) ||
          task.description.toLowerCase().includes(search) ||
          task.type.toLowerCase().includes(search)
        )
      }

      return filtered
    },

    activeTasks: (state) => state.tasks.filter(task => task.status === 'active'),
    inactiveTasks: (state) => state.tasks.filter(task => task.status === 'inactive'),
    pausedTasks: (state) => state.tasks.filter(task => task.status === 'paused'),

    tasksByType: (state) => {
      const byType = {}
      state.tasks.forEach(task => {
        byType[task.type] = (byType[task.type] || 0) + 1
      })
      return byType
    },

    upcomingTasks: (state) => {
      const now = new Date()
      return state.tasks
        .filter(task => task.status === 'active' && task.nextRunAt)
        .map(task => ({
          ...task,
          timeUntilRun: new Date(task.nextRunAt).getTime() - now.getTime()
        }))
        .filter(task => task.timeUntilRun > 0)
        .sort((a, b) => a.timeUntilRun - b.timeUntilRun)
        .slice(0, 10)
    }
  },

  actions: {
    /**
     * Fetch all tasks
     */
    async fetchTasks() {
      this.loading = true
      this.error = null

      try {
        const response = await tasksApi.getTasks()

        if (response.success) {
          this.tasks = response.data
          this.pagination.total = response.count
        } else {
          throw new Error(response.message || 'Failed to fetch tasks')
        }
      } catch (error) {
        this.error = error.response?.data?.message || error.message || 'Failed to fetch tasks'
        ElMessage.error(this.error)
      } finally {
        this.loading = false
      }
    },

    /**
     * Fetch task types
     */
    async fetchTaskTypes() {
      try {
        const response = await tasksApi.getTaskTypes()

        if (response.success) {
          this.taskTypes = response.data
        }
      } catch (error) {
        console.error('Failed to fetch task types:', error)
      }
    },

    /**
     * Fetch single task
     * @param {string} id - Task ID
     */
    async fetchTask(id) {
      this.loading = true
      this.error = null

      try {
        const response = await tasksApi.getTask(id)

        if (response.success) {
          this.selectedTask = response.data
          return response.data
        } else {
          throw new Error(response.message || 'Failed to fetch task')
        }
      } catch (error) {
        this.error = error.response?.data?.message || error.message || 'Failed to fetch task'
        ElMessage.error(this.error)
        return null
      } finally {
        this.loading = false
      }
    },

    /**
     * Create new task
     * @param {Object} taskData - Task data
     */
    async createTask(taskData) {
      this.loading = true
      this.error = null

      try {
        const response = await tasksApi.createTask(taskData)

        if (response.success) {
          this.tasks.push(response.data)
          this.pagination.total++
          ElMessage.success('Task created successfully')
          return response.data
        } else {
          throw new Error(response.message || 'Failed to create task')
        }
      } catch (error) {
        this.error = error.response?.data?.message || error.message || 'Failed to create task'
        ElMessage.error(this.error)
        throw error
      } finally {
        this.loading = false
      }
    },

    /**
     * Update task
     * @param {string} id - Task ID
     * @param {Object} taskData - Updated task data
     */
    async updateTask(id, taskData) {
      this.loading = true
      this.error = null

      try {
        const response = await tasksApi.updateTask(id, taskData)

        if (response.success) {
          const index = this.tasks.findIndex(task => task.id === id)
          if (index !== -1) {
            this.tasks[index] = response.data
          }

          if (this.selectedTask?.id === id) {
            this.selectedTask = response.data
          }

          ElMessage.success('Task updated successfully')
          return response.data
        } else {
          throw new Error(response.message || 'Failed to update task')
        }
      } catch (error) {
        this.error = error.response?.data?.message || error.message || 'Failed to update task'
        ElMessage.error(this.error)
        throw error
      } finally {
        this.loading = false
      }
    },

    /**
     * Delete task
     * @param {string} id - Task ID
     */
    async deleteTask(id) {
      try {
        await ElMessageBox.confirm(
          'This will permanently delete the task. Continue?',
          'Warning',
          {
            confirmButtonText: 'Delete',
            cancelButtonText: 'Cancel',
            type: 'warning'
          }
        )

        this.loading = true
        const response = await tasksApi.deleteTask(id)

        if (response.success) {
          this.tasks = this.tasks.filter(task => task.id !== id)
          this.pagination.total--

          if (this.selectedTask?.id === id) {
            this.selectedTask = null
          }

          ElMessage.success('Task deleted successfully')
        } else {
          throw new Error(response.message || 'Failed to delete task')
        }
      } catch (error) {
        if (error !== 'cancel') {
          this.error = error.response?.data?.message || error.message || 'Failed to delete task'
          ElMessage.error(this.error)
        }
      } finally {
        this.loading = false
      }
    },

    /**
     * Run task manually
     * @param {string} id - Task ID
     */
    async runTask(id) {
      this.loading = true

      try {
        const response = await tasksApi.runTask(id)

        if (response.success) {
          ElMessage.success('Task execution triggered successfully')

          // Update task's last run time
          const task = this.tasks.find(t => t.id === id)
          if (task) {
            task.lastRunAt = new Date().toISOString()
          }

          return response.data
        } else {
          throw new Error(response.message || 'Failed to run task')
        }
      } catch (error) {
        const errorMsg = error.response?.data?.message || error.message || 'Failed to run task'
        ElMessage.error(errorMsg)
        throw error
      } finally {
        this.loading = false
      }
    },

    /**
     * Update filters
     * @param {Object} newFilters - New filter values
     */
    updateFilters(newFilters) {
      this.filters = { ...this.filters, ...newFilters }
      this.pagination.page = 1 // Reset to first page when filtering
    },

    /**
     * Clear filters
     */
    clearFilters() {
      this.filters = {
        status: '',
        type: '',
        search: ''
      }
      this.pagination.page = 1
    },

    /**
     * Update pagination
     * @param {Object} newPagination - New pagination values
     */
    updatePagination(newPagination) {
      this.pagination = { ...this.pagination, ...newPagination }
    },

    /**
     * Clear selected task
     */
    clearSelectedTask() {
      this.selectedTask = null
    },

    /**
     * Get task by ID from store
     * @param {string} id - Task ID
     */
    getTaskById(id) {
      return this.tasks.find(task => task.id === id)
    }
  }
})
