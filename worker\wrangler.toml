name = "self-scheduling-tasks"
main = "src/index.js"
compatibility_date = "2024-01-01"
compatibility_flags = ["nodejs_compat"]

# KV命名空间绑定
[[kv_namespaces]]
binding = "TASKS_KV"
id = "33866788bd034403ae17d04a79ecea00"

# Durable Objects绑定
[[durable_objects.bindings]]
name = "TASK_DO"
class_name = "FreeAccountOptimizedTaskDO"
script_name = "self-scheduling-tasks"

[[migrations]]
tag = "v2"
new_sqlite_classes = ["FreeAccountOptimizedTaskDO"]

# 纯自调度模型不需要集中式Cron触发器
# 每个任务DO使用setAlarm()进行自主调度

# 环境变量
[vars]
ENVIRONMENT = "production"
LOG_LEVEL = "info"
CONFIG_CACHE_EXPIRY = "3600000"    # 配置缓存过期时间（毫秒）
MAX_HASH_CACHE_SIZE = "50"         # 哈希缓存最大大小
EXECUTION_TIMEOUT = "30000"        # 任务执行超时时间（毫秒）
HEALTH_CHECK_INTERVAL = "300000"   # 健康检查间隔（毫秒）

# 开发环境配置
[env.development]
name = "self-scheduling-tasks-dev"
vars = { ENVIRONMENT = "development", LOG_LEVEL = "debug" }

# 生产环境配置
[env.production]
name = "self-scheduling-tasks-prod"
vars = { ENVIRONMENT = "production", LOG_LEVEL = "warn" }

# 日志配置
[observability.logs]
enabled = true


