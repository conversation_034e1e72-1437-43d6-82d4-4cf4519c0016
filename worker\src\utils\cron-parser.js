/**
 * 简化的Cron表达式解析器
 * 专为纯自调度模型设计，支持基本的cron格式
 * 
 * 支持格式：分 时 日 月 周
 * 示例：
 * - "0 * * * *" - 每小时的0分执行
 * - "*/5 * * * *" - 每5分钟执行
 * - "0 9 * * 1" - 每周一上午9点执行
 */

export class SimplifiedCronParser {
  /**
   * 解析cron表达式并计算下次运行时间
   * @param {string} cronExpression - Cron表达式
   * @param {Date} fromTime - 起始时间（默认为当前时间）
   * @returns {Date|null} - 下次运行时间
   */
  static getNextRunTime(cronExpression, fromTime = new Date()) {
    try {
      const parts = cronExpression.trim().split(/\s+/);
      if (parts.length !== 5) {
        throw new Error('Invalid cron expression format. Expected: minute hour day month weekday');
      }

      const [minute, hour, day, month, weekday] = parts;
      const next = new Date(fromTime);
      
      // 添加1分钟确保不会返回当前时间
      next.setTime(next.getTime() + 60 * 1000);
      next.setSeconds(0, 0);

      // 处理不同的cron模式
      if (this.isEveryMinute(minute, hour, day, month, weekday)) {
        return this.calculateEveryMinute(minute, next);
      }

      if (this.isHourly(minute, hour, day, month, weekday)) {
        return this.calculateHourly(minute, next);
      }

      if (this.isDaily(minute, hour, day, month, weekday)) {
        return this.calculateDaily(minute, hour, next);
      }

      if (this.isWeekly(minute, hour, day, month, weekday)) {
        return this.calculateWeekly(minute, hour, weekday, next);
      }

      // 默认处理：1小时后执行
      console.warn(`Unsupported cron pattern: ${cronExpression}, defaulting to 1 hour interval`);
      return new Date(next.getTime() + 60 * 60 * 1000);

    } catch (error) {
      console.error('Cron parsing error:', error);
      return null;
    }
  }

  /**
   * 检查是否为每分钟执行模式
   */
  static isEveryMinute(minute, hour, day, month, weekday) {
    return minute.startsWith('*/') && hour === '*' && day === '*' && month === '*' && weekday === '*';
  }

  /**
   * 检查是否为每小时执行模式
   */
  static isHourly(minute, hour, day, month, weekday) {
    return !minute.includes('*') && hour === '*' && day === '*' && month === '*' && weekday === '*';
  }

  /**
   * 检查是否为每日执行模式
   */
  static isDaily(minute, hour, day, month, weekday) {
    return !minute.includes('*') && !hour.includes('*') && day === '*' && month === '*' && weekday === '*';
  }

  /**
   * 检查是否为每周执行模式
   */
  static isWeekly(minute, hour, day, month, weekday) {
    return !minute.includes('*') && !hour.includes('*') && day === '*' && month === '*' && !weekday.includes('*');
  }

  /**
   * 计算每分钟执行的下次时间
   */
  static calculateEveryMinute(minute, fromTime) {
    const interval = parseInt(minute.substring(2)); // 从 "*/5" 提取 5
    if (isNaN(interval) || interval <= 0) {
      throw new Error('Invalid minute interval');
    }

    const next = new Date(fromTime);
    const currentMinute = next.getMinutes();
    const nextMinute = Math.ceil(currentMinute / interval) * interval;
    
    if (nextMinute >= 60) {
      next.setHours(next.getHours() + 1);
      next.setMinutes(0);
    } else {
      next.setMinutes(nextMinute);
    }
    
    return next;
  }

  /**
   * 计算每小时执行的下次时间
   */
  static calculateHourly(minute, fromTime) {
    const targetMinute = parseInt(minute);
    if (isNaN(targetMinute) || targetMinute < 0 || targetMinute >= 60) {
      throw new Error('Invalid minute value');
    }

    const next = new Date(fromTime);
    next.setMinutes(targetMinute);
    
    // 如果目标分钟已经过了，移到下一小时
    if (next <= fromTime) {
      next.setHours(next.getHours() + 1);
    }
    
    return next;
  }

  /**
   * 计算每日执行的下次时间
   */
  static calculateDaily(minute, hour, fromTime) {
    const targetMinute = parseInt(minute);
    const targetHour = parseInt(hour);
    
    if (isNaN(targetMinute) || targetMinute < 0 || targetMinute >= 60) {
      throw new Error('Invalid minute value');
    }
    if (isNaN(targetHour) || targetHour < 0 || targetHour >= 24) {
      throw new Error('Invalid hour value');
    }

    const next = new Date(fromTime);
    next.setHours(targetHour, targetMinute, 0, 0);
    
    // 如果目标时间已经过了，移到明天
    if (next <= fromTime) {
      next.setDate(next.getDate() + 1);
    }
    
    return next;
  }

  /**
   * 计算每周执行的下次时间
   */
  static calculateWeekly(minute, hour, weekday, fromTime) {
    const targetMinute = parseInt(minute);
    const targetHour = parseInt(hour);
    const targetWeekday = parseInt(weekday); // 0=Sunday, 1=Monday, ..., 6=Saturday
    
    if (isNaN(targetMinute) || targetMinute < 0 || targetMinute >= 60) {
      throw new Error('Invalid minute value');
    }
    if (isNaN(targetHour) || targetHour < 0 || targetHour >= 24) {
      throw new Error('Invalid hour value');
    }
    if (isNaN(targetWeekday) || targetWeekday < 0 || targetWeekday > 6) {
      throw new Error('Invalid weekday value');
    }

    const next = new Date(fromTime);
    next.setHours(targetHour, targetMinute, 0, 0);
    
    const currentWeekday = next.getDay();
    let daysToAdd = targetWeekday - currentWeekday;
    
    // 如果是同一天但时间已过，或者目标是之前的日期，移到下周
    if (daysToAdd < 0 || (daysToAdd === 0 && next <= fromTime)) {
      daysToAdd += 7;
    }
    
    next.setDate(next.getDate() + daysToAdd);
    return next;
  }

  /**
   * 验证cron表达式格式
   * @param {string} cronExpression - Cron表达式
   * @returns {boolean} - 是否有效
   */
  static isValid(cronExpression) {
    try {
      if (!cronExpression || typeof cronExpression !== 'string') {
        return false;
      }

      const parts = cronExpression.trim().split(/\s+/);
      if (parts.length !== 5) {
        return false;
      }

      const [minute, hour, day, month, weekday] = parts;
      
      // 基本格式验证
      if (!this.isValidField(minute, 0, 59, true)) return false;
      if (!this.isValidField(hour, 0, 23, false)) return false;
      if (!this.isValidField(day, 1, 31, false)) return false;
      if (!this.isValidField(month, 1, 12, false)) return false;
      if (!this.isValidField(weekday, 0, 6, false)) return false;

      // 尝试计算下次运行时间来验证逻辑
      const nextRun = this.getNextRunTime(cronExpression);
      return nextRun !== null;

    } catch (error) {
      return false;
    }
  }

  /**
   * 验证单个字段
   */
  static isValidField(field, min, max, allowInterval) {
    if (field === '*') return true;
    
    // 检查间隔模式 (*/n)
    if (allowInterval && field.startsWith('*/')) {
      const interval = parseInt(field.substring(2));
      return !isNaN(interval) && interval > 0 && interval <= max;
    }
    
    // 检查数字
    const num = parseInt(field);
    return !isNaN(num) && num >= min && num <= max;
  }

  /**
   * 获取cron表达式的人类可读描述
   * @param {string} cronExpression - Cron表达式
   * @returns {string} - 描述文本
   */
  static getDescription(cronExpression) {
    try {
      const parts = cronExpression.trim().split(/\s+/);
      if (parts.length !== 5) {
        return 'Invalid cron expression';
      }

      const [minute, hour, day, month, weekday] = parts;

      if (this.isEveryMinute(minute, hour, day, month, weekday)) {
        const interval = parseInt(minute.substring(2));
        return `Every ${interval} minute${interval > 1 ? 's' : ''}`;
      }

      if (this.isHourly(minute, hour, day, month, weekday)) {
        return `Every hour at minute ${minute}`;
      }

      if (this.isDaily(minute, hour, day, month, weekday)) {
        return `Daily at ${hour.padStart(2, '0')}:${minute.padStart(2, '0')}`;
      }

      if (this.isWeekly(minute, hour, day, month, weekday)) {
        const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
        const dayName = days[parseInt(weekday)] || 'Unknown';
        return `Weekly on ${dayName} at ${hour.padStart(2, '0')}:${minute.padStart(2, '0')}`;
      }

      return 'Custom schedule';

    } catch (error) {
      return 'Invalid cron expression';
    }
  }
}
