<template>
  <div class="dynamic-form">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-position="top"
      class="form-container"
    >
      <div
        v-for="(field, key) in schema.properties"
        :key="key"
        class="form-field"
      >
        <!-- String Input -->
        <el-form-item
          v-if="field.type === 'string' && !field.enum"
          :label="field.title || key"
          :prop="key"
        >
          <el-input
            v-if="field.format === 'textarea'"
            v-model="formData[key]"
            type="textarea"
            :placeholder="field.description"
            :rows="4"
            resize="vertical"
          />
          <el-input
            v-else-if="field.format === 'password'"
            v-model="formData[key]"
            type="password"
            :placeholder="field.description"
            show-password
          />
          <el-input
            v-else-if="field.format === 'uri'"
            v-model="formData[key]"
            :placeholder="field.description || 'https://example.com'"
          >
            <template #prepend>
              <el-icon><Link /></el-icon>
            </template>
          </el-input>
          <el-input
            v-else-if="field.format === 'email'"
            v-model="formData[key]"
            :placeholder="field.description || '<EMAIL>'"
          >
            <template #prepend>
              <el-icon><Message /></el-icon>
            </template>
          </el-input>
          <el-input
            v-else
            v-model="formData[key]"
            :placeholder="field.description"
          />
          <div v-if="field.description" class="field-help">
            {{ field.description }}
          </div>
        </el-form-item>

        <!-- Select/Enum -->
        <el-form-item
          v-else-if="field.type === 'string' && field.enum"
          :label="field.title || key"
          :prop="key"
        >
          <el-select
            v-model="formData[key]"
            :placeholder="field.description || 'Select an option'"
            style="width: 100%"
          >
            <el-option
              v-for="option in field.enum"
              :key="option"
              :label="formatEnumLabel(option)"
              :value="option"
            />
          </el-select>
          <div v-if="field.description" class="field-help">
            {{ field.description }}
          </div>
        </el-form-item>

        <!-- Number Input -->
        <el-form-item
          v-else-if="field.type === 'number' || field.type === 'integer'"
          :label="field.title || key"
          :prop="key"
        >
          <el-input-number
            v-model="formData[key]"
            :min="field.minimum"
            :max="field.maximum"
            :step="field.type === 'integer' ? 1 : 0.1"
            :placeholder="field.description"
            style="width: 100%"
          />
          <div v-if="field.description" class="field-help">
            {{ field.description }}
          </div>
        </el-form-item>

        <!-- Boolean Switch -->
        <el-form-item
          v-else-if="field.type === 'boolean'"
          :label="field.title || key"
          :prop="key"
        >
          <el-switch
            v-model="formData[key]"
            :active-text="field.description"
          />
          <div v-if="field.description" class="field-help">
            {{ field.description }}
          </div>
        </el-form-item>

        <!-- Array Input -->
        <el-form-item
          v-else-if="field.type === 'array'"
          :label="field.title || key"
          :prop="key"
        >
          <div class="array-field">
            <div
              v-for="(item, index) in formData[key]"
              :key="index"
              class="array-item"
            >
              <el-input
                v-if="field.items?.type === 'string'"
                v-model="formData[key][index]"
                :placeholder="field.items?.description || 'Enter value'"
              />
              <el-input-number
                v-else-if="field.items?.type === 'number'"
                v-model="formData[key][index]"
                style="width: 100%"
              />
              <el-button
                :icon="Delete"
                circle
                size="small"
                type="danger"
                @click="removeArrayItem(key, index)"
              />
            </div>
            <el-button
              :icon="Plus"
              type="primary"
              plain
              @click="addArrayItem(key, field)"
            >
              Add {{ field.items?.title || 'Item' }}
            </el-button>
          </div>
          <div v-if="field.description" class="field-help">
            {{ field.description }}
          </div>
        </el-form-item>

        <!-- Object Input -->
        <el-form-item
          v-else-if="field.type === 'object'"
          :label="field.title || key"
          :prop="key"
        >
          <div class="object-field">
            <div
              v-for="(value, objKey) in formData[key]"
              :key="objKey"
              class="object-item"
            >
              <el-input
                v-model="objectKeys[key][objKey]"
                placeholder="Key"
                style="width: 120px"
                @blur="updateObjectKey(key, objKey, objectKeys[key][objKey])"
              />
              <el-input
                v-model="formData[key][objKey]"
                placeholder="Value"
                style="flex: 1"
              />
              <el-button
                :icon="Delete"
                circle
                size="small"
                type="danger"
                @click="removeObjectItem(key, objKey)"
              />
            </div>
            <el-button
              :icon="Plus"
              type="primary"
              plain
              @click="addObjectItem(key)"
            >
              Add Property
            </el-button>
          </div>
          <div v-if="field.description" class="field-help">
            {{ field.description }}
          </div>
        </el-form-item>
      </div>
    </el-form>
  </div>
</template>

<script setup>
import { ref, reactive, watch, computed } from 'vue'
import { Plus, Delete, Link, Message } from '@element-plus/icons-vue'

const props = defineProps({
  schema: {
    type: Object,
    required: true
  },
  modelValue: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:modelValue', 'validate'])

// Form reference
const formRef = ref()

// Form data
const formData = reactive({ ...props.modelValue })

// Object keys tracking (for dynamic object fields)
const objectKeys = reactive({})

// Initialize form data based on schema
const initializeFormData = () => {
  Object.entries(props.schema.properties || {}).forEach(([key, field]) => {
    if (!(key in formData)) {
      if (field.type === 'array') {
        formData[key] = field.default || []
      } else if (field.type === 'object') {
        formData[key] = field.default || {}
        objectKeys[key] = {}
      } else if (field.type === 'boolean') {
        formData[key] = field.default !== undefined ? field.default : false
      } else if (field.type === 'number' || field.type === 'integer') {
        formData[key] = field.default || 0
      } else {
        formData[key] = field.default || ''
      }
    }
  })
}

// Generate form validation rules
const formRules = computed(() => {
  const rules = {}
  const required = props.schema.required || []
  
  Object.entries(props.schema.properties || {}).forEach(([key, field]) => {
    const fieldRules = []
    
    // Required validation
    if (required.includes(key)) {
      fieldRules.push({
        required: true,
        message: `${field.title || key} is required`,
        trigger: 'blur'
      })
    }
    
    // Type-specific validations
    if (field.type === 'string') {
      if (field.minLength) {
        fieldRules.push({
          min: field.minLength,
          message: `Minimum length is ${field.minLength}`,
          trigger: 'blur'
        })
      }
      if (field.maxLength) {
        fieldRules.push({
          max: field.maxLength,
          message: `Maximum length is ${field.maxLength}`,
          trigger: 'blur'
        })
      }
      if (field.format === 'email') {
        fieldRules.push({
          type: 'email',
          message: 'Please enter a valid email address',
          trigger: 'blur'
        })
      }
      if (field.format === 'uri') {
        fieldRules.push({
          pattern: /^https?:\/\/.+/,
          message: 'Please enter a valid URL',
          trigger: 'blur'
        })
      }
    }
    
    if (field.type === 'number' || field.type === 'integer') {
      if (field.minimum !== undefined) {
        fieldRules.push({
          type: 'number',
          min: field.minimum,
          message: `Minimum value is ${field.minimum}`,
          trigger: 'blur'
        })
      }
      if (field.maximum !== undefined) {
        fieldRules.push({
          type: 'number',
          max: field.maximum,
          message: `Maximum value is ${field.maximum}`,
          trigger: 'blur'
        })
      }
    }
    
    if (fieldRules.length > 0) {
      rules[key] = fieldRules
    }
  })
  
  return rules
})

// Array operations
const addArrayItem = (key, field) => {
  if (!formData[key]) {
    formData[key] = []
  }
  
  if (field.items?.type === 'string') {
    formData[key].push('')
  } else if (field.items?.type === 'number') {
    formData[key].push(0)
  } else {
    formData[key].push('')
  }
}

const removeArrayItem = (key, index) => {
  formData[key].splice(index, 1)
}

// Object operations
const addObjectItem = (key) => {
  if (!formData[key]) {
    formData[key] = {}
  }
  if (!objectKeys[key]) {
    objectKeys[key] = {}
  }
  
  const newKey = `key_${Date.now()}`
  formData[key][newKey] = ''
  objectKeys[key][newKey] = newKey
}

const removeObjectItem = (key, objKey) => {
  delete formData[key][objKey]
  delete objectKeys[key][objKey]
}

const updateObjectKey = (key, oldKey, newKey) => {
  if (oldKey !== newKey && newKey) {
    const value = formData[key][oldKey]
    delete formData[key][oldKey]
    formData[key][newKey] = value
    
    delete objectKeys[key][oldKey]
    objectKeys[key][newKey] = newKey
  }
}

// Utility functions
const formatEnumLabel = (value) => {
  return value.split('_').map(word => 
    word.charAt(0).toUpperCase() + word.slice(1)
  ).join(' ')
}

// Validation
const validate = async () => {
  if (!formRef.value) return false
  
  try {
    await formRef.value.validate()
    return true
  } catch (error) {
    return false
  }
}

// Watch for changes and emit
watch(formData, (newValue) => {
  emit('update:modelValue', { ...newValue })
}, { deep: true })

// Initialize on mount
initializeFormData()

// Expose validation method
defineExpose({
  validate
})
</script>

<style lang="scss" scoped>
.dynamic-form {
  .form-container {
    max-width: 600px;
  }
  
  .form-field {
    margin-bottom: var(--app-spacing-lg);
  }
  
  .field-help {
    font-size: 12px;
    color: var(--el-text-color-secondary);
    margin-top: 4px;
    line-height: 1.4;
  }
  
  .array-field {
    border: 1px solid var(--el-border-color-light);
    border-radius: var(--app-border-radius);
    padding: var(--app-spacing-md);
    background: var(--el-fill-color-lighter);
  }
  
  .array-item {
    display: flex;
    align-items: center;
    gap: var(--app-spacing-sm);
    margin-bottom: var(--app-spacing-sm);
    
    &:last-child {
      margin-bottom: var(--app-spacing-md);
    }
    
    .el-input,
    .el-input-number {
      flex: 1;
    }
  }
  
  .object-field {
    border: 1px solid var(--el-border-color-light);
    border-radius: var(--app-border-radius);
    padding: var(--app-spacing-md);
    background: var(--el-fill-color-lighter);
  }
  
  .object-item {
    display: flex;
    align-items: center;
    gap: var(--app-spacing-sm);
    margin-bottom: var(--app-spacing-sm);
    
    &:last-child {
      margin-bottom: var(--app-spacing-md);
    }
  }
}
</style>
