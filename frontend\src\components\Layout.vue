<template>
  <div class="layout-container">
    <!-- Header -->
    <el-header class="app-header">
      <div class="header-content">
        <div class="header-left">
          <el-button
            v-if="isMobile"
            :icon="Menu"
            circle
            @click="toggleSidebar"
          />
          <h1 class="app-title">
            <el-icon class="title-icon"><Timer /></el-icon>
            {{ $t('app.title') }}
          </h1>
        </div>

        <div class="header-right">
          <el-badge :value="alertCount" :hidden="alertCount === 0" class="alert-badge">
            <el-button :icon="Bell" circle />
          </el-badge>

          <el-dropdown @command="handleUserAction">
            <el-button circle>
              <el-icon><User /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="profile">
                  <el-icon><User /></el-icon>
                  {{ $t('nav.profile') }}
                </el-dropdown-item>
                <el-dropdown-item command="settings">
                  <el-icon><Setting /></el-icon>
                  {{ $t('nav.settings') }}
                </el-dropdown-item>
                <el-dropdown-item divided command="logout">
                  <el-icon><SwitchButton /></el-icon>
                  {{ $t('nav.logout') }}
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
    </el-header>

    <el-container class="main-container">
      <!-- Sidebar -->
      <el-aside
        :class="['app-sidebar', { 'sidebar-collapsed': isMobile && !sidebarVisible }]"
        :width="sidebarWidth"
      >
        <el-menu
          :default-active="currentRoute"
          class="sidebar-menu"
          router
          :collapse="isMobile && !sidebarVisible"
        >
          <el-menu-item index="/dashboard">
            <el-icon><Monitor /></el-icon>
            <span>{{ $t('nav.dashboard') }}</span>
          </el-menu-item>

          <el-menu-item index="/tasks">
            <el-icon><Timer /></el-icon>
            <span>{{ $t('nav.tasks') }}</span>
            <el-badge
              v-if="tasksStore.activeTasks.length > 0"
              :value="tasksStore.activeTasks.length"
              class="menu-badge"
            />
          </el-menu-item>

          <el-menu-item index="/logs">
            <el-icon><Document /></el-icon>
            <span>{{ $t('nav.logs') }}</span>
          </el-menu-item>

          <el-menu-item index="/cron">
            <el-icon><Setting /></el-icon>
            <span>{{ $t('nav.cron') }}</span>
          </el-menu-item>
        </el-menu>
      </el-aside>

      <!-- Main Content -->
      <el-main class="app-main">
        <div class="page-container">
          <router-view v-slot="{ Component }">
            <transition name="fade" mode="out-in">
              <component :is="Component" />
            </transition>
          </router-view>
        </div>
      </el-main>
    </el-container>

    <!-- Mobile Sidebar Overlay -->
    <div
      v-if="isMobile && sidebarVisible"
      class="sidebar-overlay"
      @click="closeSidebar"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useTasksStore } from '@/stores/tasks'
import {
  Menu, Timer, Bell, User, Setting, SwitchButton,
  Monitor, Document
} from '@element-plus/icons-vue'

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()
const tasksStore = useTasksStore()

// Responsive state
const windowWidth = ref(window.innerWidth)
const sidebarVisible = ref(false)

const isMobile = computed(() => windowWidth.value < 768)
const sidebarWidth = computed(() => isMobile.value ? '100%' : '240px')
const currentRoute = computed(() => route.path)

// Mock alert count - in real app, this would come from a store
const alertCount = computed(() => {
  // Count recent failures or system alerts
  return 0
})

// Window resize handler
const handleResize = () => {
  windowWidth.value = window.innerWidth
  if (!isMobile.value) {
    sidebarVisible.value = false
  }
}

// Sidebar controls
const toggleSidebar = () => {
  sidebarVisible.value = !sidebarVisible.value
}

const closeSidebar = () => {
  sidebarVisible.value = false
}

// User actions
const handleUserAction = async (command) => {
  switch (command) {
    case 'profile':
      // Handle profile action
      break
    case 'settings':
      // Handle settings action
      break
    case 'logout':
      await authStore.logout()
      router.push('/login')
      break
  }
}

// Lifecycle
onMounted(() => {
  window.addEventListener('resize', handleResize)

  // Load initial data
  tasksStore.fetchTasks()
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})
</script>

<style lang="scss" scoped>
.layout-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.app-header {
  display: flex;
  align-items: center;
  padding: 0 var(--app-spacing-lg);
  background: var(--el-bg-color);
  border-bottom: 1px solid var(--el-border-color-light);
  box-shadow: var(--app-shadow-light);
  z-index: 1000;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.header-left {
  display: flex;
  align-items: center;
  gap: var(--app-spacing-md);
}

.app-title {
  display: flex;
  align-items: center;
  gap: var(--app-spacing-sm);
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: var(--el-text-color-primary);

  .title-icon {
    color: var(--el-color-primary);
  }
}

.header-right {
  display: flex;
  align-items: center;
  gap: var(--app-spacing-md);
}

.alert-badge {
  :deep(.el-badge__content) {
    top: 8px;
    right: 8px;
  }
}

.main-container {
  flex: 1;
  height: calc(100vh - var(--app-header-height));
}

.app-sidebar {
  background: var(--el-bg-color);
  border-right: 1px solid var(--el-border-color-light);
  box-shadow: var(--app-shadow-light);
  transition: transform 0.3s ease;

  &.sidebar-collapsed {
    transform: translateX(-100%);
  }
}

.sidebar-menu {
  border: none;
  height: 100%;

  .el-menu-item {
    height: 56px;
    line-height: 56px;

    &.is-active {
      background: var(--el-color-primary-light-9);
      color: var(--el-color-primary);
      border-right: 3px solid var(--el-color-primary);
    }
  }
}

.menu-badge {
  margin-left: auto;

  :deep(.el-badge__content) {
    position: static;
    transform: none;
    font-size: 10px;
    padding: 0 4px;
    height: 16px;
    line-height: 16px;
    min-width: 16px;
  }
}

.app-main {
  padding: 0;
  background: var(--el-bg-color-page);
  overflow-y: auto;
}

.page-container {
  padding: var(--app-spacing-lg);
  min-height: 100%;
}

.sidebar-overlay {
  position: fixed;
  top: var(--app-header-height);
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 999;
}

// Mobile styles
@media (max-width: 768px) {
  .app-title {
    font-size: 18px;

    span {
      display: none;
    }
  }

  .app-sidebar {
    position: fixed;
    top: var(--app-header-height);
    left: 0;
    height: calc(100vh - var(--app-header-height));
    z-index: 1000;
  }

  .page-container {
    padding: var(--app-spacing-md);
  }
}

// Transitions
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
