<template>
  <div class="task-form">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-position="top"
      class="main-form"
    >
      <!-- Basic Information -->
      <div class="form-section">
        <h3 class="section-title">{{ $t('tasks.form.basicInfo') }}</h3>

        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item :label="$t('tasks.form.name')" prop="name">
              <el-input
                v-model="formData.name"
                :placeholder="$t('tasks.form.namePlaceholder')"
              />
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item :label="$t('tasks.form.type')" prop="type">
              <el-select
                v-model="formData.type"
                :placeholder="$t('tasks.form.typePlaceholder')"
                style="width: 100%"
                @change="handleTypeChange"
              >
                <el-option
                  v-for="type in taskTypes"
                  :key="type.type"
                  :label="type.name"
                  :value="type.type"
                >
                  <div class="type-option">
                    <span class="type-name">{{ type.name }}</span>
                    <span class="type-description">{{ type.description }}</span>
                  </div>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item :label="$t('tasks.form.description')" prop="description">
          <el-input
            v-model="formData.description"
            type="textarea"
            :rows="3"
            :placeholder="$t('tasks.form.descriptionPlaceholder')"
          />
        </el-form-item>
      </div>

      <!-- Schedule Configuration -->
      <div class="form-section">
        <h3 class="section-title">{{ $t('tasks.form.schedule') }}</h3>

        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item :label="$t('tasks.form.schedule')" prop="cron">
              <el-input
                v-model="formData.cron"
                :placeholder="$t('tasks.form.schedulePlaceholder')"
              >
                <template #append>
                  <el-button @click="showCronHelper">
                    <el-icon><QuestionFilled /></el-icon>
                  </el-button>
                </template>
              </el-input>
              <div class="field-help">
                {{ getCronDescription(formData.cron) }}
              </div>
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item :label="$t('common.status')" prop="status">
              <el-select v-model="formData.status" style="width: 100%">
                <el-option :label="$t('tasks.status.active')" value="active" />
                <el-option :label="$t('tasks.status.inactive')" value="inactive" />
                <el-option :label="$t('tasks.status.paused')" value="paused" />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="时区" prop="timezone">
              <el-select v-model="formData.timezone" style="width: 100%">
                <el-option label="UTC" value="UTC" />
                <el-option label="本地时区" value="local" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- Task Configuration -->
      <div v-if="selectedTaskType" class="form-section">
        <h3 class="section-title">{{ selectedTaskType.name }} {{ $t('tasks.form.configuration') }}</h3>
        <p class="section-description">{{ selectedTaskType.description }}</p>

        <DynamicForm
          ref="dynamicFormRef"
          v-model="formData.config"
          :schema="selectedTaskType.configSchema"
        />
      </div>

      <!-- Advanced Settings -->
      <div class="form-section">
        <h3 class="section-title">高级设置</h3>

        <el-row :gutter="16">
          <el-col :span="8">
            <el-form-item label="超时时间 (毫秒)" prop="timeout">
              <el-input-number
                v-model="formData.timeout"
                :min="1000"
                :max="300000"
                :step="1000"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item label="启用重试">
              <el-switch v-model="formData.retryConfig.enabled" />
            </el-form-item>
          </el-col>
        </el-row>

        <div v-if="formData.retryConfig.enabled" class="retry-config">
          <el-row :gutter="16">
            <el-col :span="6">
              <el-form-item label="最大重试次数">
                <el-input-number
                  v-model="formData.retryConfig.maxRetries"
                  :min="1"
                  :max="10"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>

            <el-col :span="6">
              <el-form-item label="重试策略">
                <el-select v-model="formData.retryConfig.strategy" style="width: 100%">
                  <el-option label="固定间隔" value="fixed" />
                  <el-option label="线性递增" value="linear" />
                  <el-option label="指数递增" value="exponential" />
                </el-select>
              </el-form-item>
            </el-col>

            <el-col :span="6">
              <el-form-item label="基础延迟 (毫秒)">
                <el-input-number
                  v-model="formData.retryConfig.baseDelay"
                  :min="1000"
                  :max="60000"
                  :step="1000"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>

            <el-col :span="6">
              <el-form-item label="最大延迟 (毫秒)">
                <el-input-number
                  v-model="formData.retryConfig.maxDelay"
                  :min="1000"
                  :max="300000"
                  :step="1000"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <el-form-item label="标签">
          <el-select
            v-model="formData.tags"
            multiple
            filterable
            allow-create
            placeholder="添加标签"
            style="width: 100%"
          />
        </el-form-item>
      </div>
    </el-form>

    <!-- Form Actions -->
    <div class="form-actions">
      <el-button @click="$emit('cancel')">{{ $t('tasks.form.cancel') }}</el-button>
      <el-button type="primary" @click="handleSubmit" :loading="submitting">
        {{ mode === 'create' ? $t('common.create') : $t('common.update') }}
      </el-button>
    </div>

    <!-- Cron Helper Dialog -->
    <el-dialog v-model="cronHelperVisible" title="Cron表达式助手" width="600px">
      <div class="cron-helper">
        <p>Cron表达式格式: <code>分钟 小时 日 月 星期</code></p>

        <div class="cron-examples">
          <h4>常用示例:</h4>
          <div class="example-list">
            <div class="example-item" @click="setCronExpression('*/5 * * * *')">
              <code>*/5 * * * *</code> - 每5分钟
            </div>
            <div class="example-item" @click="setCronExpression('0 */1 * * *')">
              <code>0 */1 * * *</code> - 每小时
            </div>
            <div class="example-item" @click="setCronExpression('0 9 * * *')">
              <code>0 9 * * *</code> - 每天上午9点
            </div>
            <div class="example-item" @click="setCronExpression('0 0 * * 0')">
              <code>0 0 * * 0</code> - 每周日午夜
            </div>
            <div class="example-item" @click="setCronExpression('0 0 1 * *')">
              <code>0 0 1 * *</code> - 每月1日午夜
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import DynamicForm from './DynamicForm.vue'
import { QuestionFilled } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
  task: {
    type: Object,
    default: null
  },
  taskTypes: {
    type: Array,
    default: () => []
  },
  mode: {
    type: String,
    default: 'create' // 'create' or 'edit'
  }
})

const emit = defineEmits(['submit', 'cancel'])

const { t } = useI18n()

// Form refs
const formRef = ref()
const dynamicFormRef = ref()

// Form state
const submitting = ref(false)
const cronHelperVisible = ref(false)

// Form data
const formData = reactive({
  name: '',
  description: '',
  type: '',
  status: 'inactive',
  cron: '0 */1 * * *',
  timezone: 'UTC',
  config: {},
  timeout: 30000,
  retryConfig: {
    enabled: false,
    maxRetries: 3,
    strategy: 'exponential',
    baseDelay: 1000,
    maxDelay: 60000
  },
  tags: [],
  metadata: {}
})

// Initialize form data
if (props.task) {
  Object.assign(formData, props.task)
}

// Methods (defined before use)
const validateCronExpression = (rule, value, callback) => {
  if (!value) {
    callback(new Error(t('tasks.form.scheduleRequired')))
    return
  }

  // Basic cron validation (5 parts)
  const parts = value.trim().split(/\s+/)
  if (parts.length !== 5) {
    callback(new Error(t('tasks.form.scheduleInvalid')))
    return
  }

  callback()
}

const handleTypeChange = () => {
  // Reset config when type changes
  formData.config = {}
}

// Form validation rules (now can safely use validateCronExpression)
const formRules = {
  name: [
    { required: true, message: t('tasks.form.nameRequired'), trigger: 'blur' },
    { min: 1, max: 100, message: '名称长度应为1-100个字符', trigger: 'blur' }
  ],
  type: [
    { required: true, message: t('tasks.form.typeRequired'), trigger: 'change' }
  ],
  cron: [
    { required: true, message: t('tasks.form.scheduleRequired'), trigger: 'blur' },
    { validator: validateCronExpression, trigger: 'blur' }
  ],
  status: [
    { required: true, message: '状态不能为空', trigger: 'change' }
  ],
  timeout: [
    { type: 'number', min: 1000, max: 300000, message: '超时时间必须在1000-300000毫秒之间', trigger: 'blur' }
  ]
}

// Computed
const selectedTaskType = computed(() => {
  return props.taskTypes?.find(type => type.type === formData.type)
})

const getCronDescription = (cron) => {
  if (!cron) return ''

  // Simple cron description
  const descriptions = {
    '*/1 * * * *': '每分钟',
    '*/5 * * * *': '每5分钟',
    '*/15 * * * *': '每15分钟',
    '*/30 * * * *': '每30分钟',
    '0 */1 * * *': '每小时',
    '0 */6 * * *': '每6小时',
    '0 */12 * * *': '每12小时',
    '0 0 * * *': '每天午夜',
    '0 9 * * *': '每天上午9点',
    '0 0 * * 0': '每周日午夜',
    '0 0 1 * *': '每月1日午夜'
  }

  return descriptions[cron] || '自定义计划'
}

const showCronHelper = () => {
  cronHelperVisible.value = true
}

const setCronExpression = (expression) => {
  formData.cron = expression
  cronHelperVisible.value = false
}

const handleSubmit = async () => {
  try {
    submitting.value = true

    // Validate main form
    await formRef.value.validate()

    // Validate dynamic form if it exists
    if (dynamicFormRef.value) {
      const isValid = await dynamicFormRef.value.validate()
      if (!isValid) {
        ElMessage.error('请修复配置错误')
        return
      }
    }

    // Submit form data
    emit('submit', { ...formData })

  } catch (error) {
    console.error('Form validation error:', error)
  } finally {
    submitting.value = false
  }
}

// Watch for task type changes to update config
watch(() => formData.type, (newType) => {
  if (newType && selectedTaskType.value) {
    // Initialize config with default values from schema
    const schema = selectedTaskType.value.configSchema
    const defaultConfig = {}

    Object.entries(schema.properties || {}).forEach(([key, field]) => {
      if (field.default !== undefined) {
        defaultConfig[key] = field.default
      }
    })

    formData.config = { ...defaultConfig, ...formData.config }
  }
})
</script>

<style lang="scss" scoped>
.task-form {
  .main-form {
    margin-bottom: var(--app-spacing-lg);
  }

  .form-section {
    margin-bottom: var(--app-spacing-xl);

    .section-title {
      margin: 0 0 var(--app-spacing-sm) 0;
      font-size: 16px;
      font-weight: 600;
      color: var(--el-text-color-primary);
    }

    .section-description {
      margin: 0 0 var(--app-spacing-md) 0;
      font-size: 14px;
      color: var(--el-text-color-secondary);
      line-height: 1.5;
    }
  }

  .field-help {
    font-size: 12px;
    color: var(--el-text-color-secondary);
    margin-top: 4px;
  }

  .retry-config {
    background: var(--el-fill-color-lighter);
    padding: var(--app-spacing-md);
    border-radius: var(--app-border-radius);
    margin-top: var(--app-spacing-md);
  }

  .form-actions {
    display: flex;
    justify-content: flex-end;
    gap: var(--app-spacing-sm);
    padding-top: var(--app-spacing-lg);
    border-top: 1px solid var(--el-border-color-light);
  }
}

.type-option {
  display: flex;
  flex-direction: column;

  .type-name {
    font-weight: 500;
  }

  .type-description {
    font-size: 12px;
    color: var(--el-text-color-secondary);
  }
}

.cron-helper {
  .cron-examples {
    margin-top: var(--app-spacing-lg);

    h4 {
      margin: 0 0 var(--app-spacing-md) 0;
      font-size: 14px;
      font-weight: 600;
    }
  }

  .example-list {
    display: flex;
    flex-direction: column;
    gap: var(--app-spacing-sm);
  }

  .example-item {
    padding: var(--app-spacing-sm);
    background: var(--el-fill-color-light);
    border-radius: var(--app-border-radius);
    cursor: pointer;
    transition: background-color 0.3s ease;

    &:hover {
      background: var(--el-fill-color);
    }

    code {
      font-family: 'Courier New', monospace;
      font-weight: 600;
      margin-right: var(--app-spacing-sm);
    }
  }
}
</style>
