<template>
  <div class="login-container">
    <div class="login-card">
      <div class="login-header">
        <div class="logo">
          <el-icon class="logo-icon"><Timer /></el-icon>
          <h1 class="logo-text">{{ $t('app.title') }}</h1>
        </div>
        <p class="login-subtitle">{{ $t('login.subtitle') }}</p>
      </div>

      <el-form
        ref="loginFormRef"
        :model="loginForm"
        :rules="loginRules"
        class="login-form"
        @submit.prevent="handleLogin"
      >
        <el-form-item prop="password">
          <el-input
            v-model="loginForm.password"
            type="password"
            :placeholder="$t('login.passwordPlaceholder')"
            size="large"
            show-password
            :prefix-icon="Lock"
            @keyup.enter="handleLogin"
            :disabled="authStore.loading"
          />
        </el-form-item>

        <el-form-item>
          <el-button
            type="primary"
            size="large"
            class="login-button"
            :loading="authStore.loading"
            @click="handleLogin"
          >
            <span v-if="!authStore.loading">{{ $t('login.signIn') }}</span>
            <span v-else>{{ $t('login.signingIn') }}</span>
          </el-button>
        </el-form-item>
      </el-form>

      <div class="login-footer">
        <el-alert
          v-if="authStore.error"
          :title="authStore.error"
          type="error"
          :closable="false"
          show-icon
        />

        <div class="login-info">
          <el-icon><InfoFilled /></el-icon>
          <span>{{ $t('login.infoText') }}</span>
        </div>
      </div>
    </div>

    <!-- Background decoration -->
    <div class="background-decoration">
      <div class="decoration-circle circle-1"></div>
      <div class="decoration-circle circle-2"></div>
      <div class="decoration-circle circle-3"></div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { useAuthStore } from '@/stores/auth'
import { Timer, Lock, InfoFilled } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

const router = useRouter()
const authStore = useAuthStore()
const { t } = useI18n()

// Form refs
const loginFormRef = ref()

// Form data
const loginForm = reactive({
  password: ''
})

// Form validation rules
const loginRules = {
  password: [
    { required: true, message: t('login.passwordRequired'), trigger: 'blur' },
    { min: 1, message: t('login.passwordMinLength'), trigger: 'blur' }
  ]
}

// Handle login
const handleLogin = async () => {
  if (!loginFormRef.value) return

  try {
    // Validate form
    const valid = await loginFormRef.value.validate()
    if (!valid) return

    // Attempt login
    const result = await authStore.login(loginForm.password)

    if (result.success) {
      ElMessage.success(t('login.loginSuccess'))
      router.push('/dashboard')
    }
  } catch (error) {
    console.error('Login error:', error)
  }
}

// Auto-focus password input on mount
onMounted(() => {
  // Clear any existing auth errors
  authStore.error = null
})
</script>

<style lang="scss" scoped>
.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: var(--app-spacing-lg);
  position: relative;
  overflow: hidden;
}

.login-card {
  background: var(--el-bg-color);
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  padding: var(--app-spacing-xl);
  width: 100%;
  max-width: 400px;
  position: relative;
  z-index: 10;
}

.login-header {
  text-align: center;
  margin-bottom: var(--app-spacing-xl);
}

.logo {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--app-spacing-sm);
  margin-bottom: var(--app-spacing-md);
}

.logo-icon {
  font-size: 32px;
  color: var(--el-color-primary);
}

.logo-text {
  margin: 0;
  font-size: 24px;
  font-weight: 700;
  color: var(--el-text-color-primary);
  background: linear-gradient(135deg, var(--el-color-primary), #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.login-subtitle {
  margin: 0;
  color: var(--el-text-color-secondary);
  font-size: 14px;
}

.login-form {
  margin-bottom: var(--app-spacing-lg);
}

.login-button {
  width: 100%;
  height: 48px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 8px;
  background: linear-gradient(135deg, var(--el-color-primary), #764ba2);
  border: none;

  &:hover {
    background: linear-gradient(135deg, var(--el-color-primary-light-3), #8e5cb8);
  }
}

.login-footer {
  .el-alert {
    margin-bottom: var(--app-spacing-md);
    border-radius: 8px;
  }
}

.login-info {
  display: flex;
  align-items: center;
  gap: var(--app-spacing-sm);
  color: var(--el-text-color-secondary);
  font-size: 12px;
  text-align: center;
  justify-content: center;
}

// Background decoration
.background-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;
}

.circle-1 {
  width: 200px;
  height: 200px;
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.circle-2 {
  width: 150px;
  height: 150px;
  top: 60%;
  right: 10%;
  animation-delay: 2s;
}

.circle-3 {
  width: 100px;
  height: 100px;
  bottom: 20%;
  left: 20%;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

// Responsive design
@media (max-width: 480px) {
  .login-container {
    padding: var(--app-spacing-md);
  }

  .login-card {
    padding: var(--app-spacing-lg);
  }

  .logo-text {
    font-size: 20px;
  }

  .decoration-circle {
    display: none;
  }
}

// Dark mode adjustments
@media (prefers-color-scheme: dark) {
  .login-container {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  }

  .decoration-circle {
    background: rgba(255, 255, 255, 0.05);
  }
}
</style>
