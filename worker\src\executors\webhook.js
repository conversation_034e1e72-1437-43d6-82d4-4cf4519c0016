/**
 * Webhook Executor
 */

export const WebhookExecutor = {
  type: 'webhook',
  name: 'Webhook',
  description: 'Sends webhook notifications with optional signature verification',
  icon: 'webhook',

  /**
   * Configuration schema for frontend form generation
   */
  configSchema: {
    type: 'object',
    required: ['url', 'payload'],
    properties: {
      url: {
        type: 'string',
        title: 'Webhook URL',
        description: 'Target URL for the webhook',
        format: 'uri'
      },
      method: {
        type: 'string',
        title: 'HTTP Method',
        description: 'HTTP method to use',
        enum: ['POST', 'PUT', 'PATCH'],
        default: 'POST'
      },
      payload: {
        type: 'object',
        title: 'Payload',
        description: 'JSON payload to send',
        additionalProperties: true
      },
      headers: {
        type: 'object',
        title: 'Custom Headers',
        description: 'Additional HTTP headers to include',
        additionalProperties: {
          type: 'string'
        }
      },
      secret: {
        type: 'string',
        title: 'Webhook Secret',
        description: 'Secret key for generating HMAC signature (optional)',
        format: 'password'
      },
      signatureHeader: {
        type: 'string',
        title: 'Signature Header',
        description: 'Header name for the signature',
        default: 'X-Webhook-Signature'
      },
      signatureAlgorithm: {
        type: 'string',
        title: 'Signature Algorithm',
        description: 'Algorithm for generating signature',
        enum: ['sha256', 'sha1'],
        default: 'sha256'
      },
      contentType: {
        type: 'string',
        title: 'Content Type',
        description: 'Content-Type header value',
        enum: ['application/json', 'application/x-www-form-urlencoded'],
        default: 'application/json'
      },
      timeout: {
        type: 'integer',
        title: 'Timeout (ms)',
        description: 'Request timeout in milliseconds',
        minimum: 1000,
        maximum: 30000,
        default: 10000
      }
    }
  },

  /**
   * Validates configuration
   * @param {Object} config - Configuration to validate
   * @returns {Object} - Validation result
   */
  validateConfig(config) {
    const errors = [];

    // Validate URL
    if (!config.url) {
      errors.push('Webhook URL is required');
    } else {
      try {
        new URL(config.url);
      } catch (error) {
        errors.push('Invalid webhook URL format');
      }
    }

    // Validate method
    const validMethods = ['POST', 'PUT', 'PATCH'];
    if (config.method && !validMethods.includes(config.method.toUpperCase())) {
      errors.push(`Invalid HTTP method: ${config.method}`);
    }

    // Validate payload
    if (!config.payload) {
      errors.push('Payload is required');
    } else if (typeof config.payload !== 'object') {
      errors.push('Payload must be an object');
    }

    // Validate headers
    if (config.headers && typeof config.headers !== 'object') {
      errors.push('Headers must be an object');
    }

    // Validate signature algorithm
    if (config.signatureAlgorithm && !['sha256', 'sha1'].includes(config.signatureAlgorithm)) {
      errors.push('Invalid signature algorithm');
    }

    // Validate timeout
    if (config.timeout && (config.timeout < 1000 || config.timeout > 30000)) {
      errors.push('Timeout must be between 1000 and 30000 milliseconds');
    }

    return {
      valid: errors.length === 0,
      errors
    };
  },

  /**
   * Executes the webhook
   * @param {Object} config - Task configuration
   * @param {Object} env - Environment bindings
   * @returns {Promise<Object>} - Execution result
   */
  async execute(config, env) {
    const startTime = Date.now();
    
    try {
      // Prepare payload
      const payload = {
        timestamp: new Date().toISOString(),
        event: 'scheduled_task',
        ...config.payload
      };

      const payloadString = JSON.stringify(payload);

      // Prepare headers
      const headers = {
        'Content-Type': config.contentType || 'application/json',
        'User-Agent': 'Cron-Task-Worker/1.0',
        'Content-Length': payloadString.length.toString(),
        ...config.headers
      };

      // Generate signature if secret is provided
      if (config.secret) {
        const signature = await this.generateSignature(
          payloadString, 
          config.secret, 
          config.signatureAlgorithm || 'sha256'
        );
        
        const signatureHeader = config.signatureHeader || 'X-Webhook-Signature';
        headers[signatureHeader] = signature;
      }

      // Prepare request options
      const requestOptions = {
        method: config.method || 'POST',
        headers,
        body: payloadString
      };

      console.log(`Sending webhook: ${requestOptions.method} ${config.url}`);

      // Make the request with timeout
      const controller = new AbortController();
      const timeout = config.timeout || 10000;
      
      const timeoutId = setTimeout(() => controller.abort(), timeout);
      
      try {
        const response = await fetch(config.url, {
          ...requestOptions,
          signal: controller.signal
        });
        
        clearTimeout(timeoutId);
        
        const endTime = Date.now();
        const duration = endTime - startTime;

        // Read response body
        let responseBody;
        const contentType = response.headers.get('content-type') || '';
        
        try {
          if (contentType.includes('application/json')) {
            responseBody = await response.json();
          } else {
            responseBody = await response.text();
          }
        } catch (error) {
          responseBody = `Error reading response: ${error.message}`;
        }

        // Check if webhook was successful (2xx status codes)
        const isSuccess = response.status >= 200 && response.status < 300;

        const result = {
          success: isSuccess,
          request: {
            url: config.url,
            method: requestOptions.method,
            headers: requestOptions.headers,
            payload: payload
          },
          response: {
            status: response.status,
            statusText: response.statusText,
            headers: Object.fromEntries(response.headers.entries()),
            body: responseBody
          },
          timing: {
            startTime: new Date(startTime).toISOString(),
            endTime: new Date(endTime).toISOString(),
            duration
          }
        };

        if (!isSuccess) {
          throw new Error(`Webhook failed with status ${response.status}: ${response.statusText}`);
        }

        console.log(`Webhook sent successfully in ${duration}ms`);
        return result;

      } catch (error) {
        clearTimeout(timeoutId);
        throw error;
      }

    } catch (error) {
      const endTime = Date.now();
      const duration = endTime - startTime;

      console.error('Webhook execution failed:', error);

      // Return detailed error information
      const result = {
        success: false,
        request: {
          url: config.url,
          method: config.method || 'POST',
          payload: config.payload
        },
        error: {
          name: error.name,
          message: error.message,
          stack: error.stack
        },
        timing: {
          startTime: new Date(startTime).toISOString(),
          endTime: new Date(endTime).toISOString(),
          duration
        }
      };

      throw new Error(`Webhook execution failed: ${error.message}`);
    }
  },

  /**
   * Generates HMAC signature for webhook payload
   * @param {string} payload - Payload string
   * @param {string} secret - Secret key
   * @param {string} algorithm - Hash algorithm (sha256 or sha1)
   * @returns {Promise<string>} - Signature string
   */
  async generateSignature(payload, secret, algorithm = 'sha256') {
    try {
      const encoder = new TextEncoder();
      const keyData = encoder.encode(secret);
      const payloadData = encoder.encode(payload);

      // Import the secret key
      const cryptoKey = await crypto.subtle.importKey(
        'raw',
        keyData,
        { name: 'HMAC', hash: algorithm.toUpperCase() },
        false,
        ['sign']
      );

      // Generate signature
      const signatureBuffer = await crypto.subtle.sign('HMAC', cryptoKey, payloadData);
      const signatureArray = new Uint8Array(signatureBuffer);
      
      // Convert to hex string
      const signatureHex = Array.from(signatureArray)
        .map(byte => byte.toString(16).padStart(2, '0'))
        .join('');

      return `${algorithm}=${signatureHex}`;

    } catch (error) {
      console.error('Error generating webhook signature:', error);
      throw new Error('Failed to generate webhook signature');
    }
  }
};
