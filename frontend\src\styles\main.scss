// @import './variables.scss';

// Global component styles
.app-card {
  background: var(--el-bg-color);
  border: 1px solid var(--el-border-color-light);
  border-radius: var(--app-border-radius);
  box-shadow: var(--app-shadow-light);
  transition: box-shadow 0.3s ease;

  &:hover {
    box-shadow: var(--app-shadow-medium);
  }
}

.app-header {
  height: var(--app-header-height);
  background: var(--el-bg-color);
  border-bottom: 1px solid var(--el-border-color-light);
  box-shadow: var(--app-shadow-light);
  z-index: 1000;
}

.app-sidebar {
  width: var(--app-sidebar-width);
  background: var(--el-bg-color);
  border-right: 1px solid var(--el-border-color-light);
  box-shadow: var(--app-shadow-light);
  z-index: 999;
}

.app-main {
  flex: 1;
  padding: var(--app-spacing-lg);
  background: var(--el-bg-color-page);
  min-height: calc(100vh - var(--app-header-height));
}

// Status indicators
.status-indicator {
  display: inline-flex;
  align-items: center;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;

  &.status-active {
    background: rgba(103, 194, 58, 0.1);
    color: var(--task-active);
  }

  &.status-inactive {
    background: rgba(144, 147, 153, 0.1);
    color: var(--task-inactive);
  }

  &.status-paused {
    background: rgba(230, 162, 60, 0.1);
    color: var(--task-paused);
  }

  &.status-success {
    background: rgba(103, 194, 58, 0.1);
    color: var(--status-success);
  }

  &.status-failure {
    background: rgba(245, 108, 108, 0.1);
    color: var(--status-danger);
  }

  &.status-timeout {
    background: rgba(230, 162, 60, 0.1);
    color: var(--status-warning);
  }
}

// Log level indicators
.log-level {
  display: inline-flex;
  align-items: center;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;

  &.level-error {
    background: rgba(245, 108, 108, 0.1);
    color: var(--log-error);
  }

  &.level-warn {
    background: rgba(230, 162, 60, 0.1);
    color: var(--log-warn);
  }

  &.level-info {
    background: rgba(64, 158, 255, 0.1);
    color: var(--log-info);
  }

  &.level-debug {
    background: rgba(144, 147, 153, 0.1);
    color: var(--log-debug);
  }
}

// Loading states
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  color: var(--el-text-color-secondary);
}

.loading-text {
  margin-left: var(--app-spacing-sm);
  font-size: 14px;
}

// Empty states
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  color: var(--el-text-color-secondary);

  .empty-icon {
    font-size: 48px;
    margin-bottom: var(--app-spacing-md);
    opacity: 0.5;
  }

  .empty-title {
    font-size: 18px;
    font-weight: 500;
    margin-bottom: var(--app-spacing-sm);
  }

  .empty-description {
    font-size: 14px;
    text-align: center;
    max-width: 400px;
    line-height: 1.5;
  }
}

// Form styles
.form-section {
  margin-bottom: var(--app-spacing-xl);

  .section-title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: var(--app-spacing-md);
    color: var(--el-text-color-primary);
  }

  .section-description {
    font-size: 14px;
    color: var(--el-text-color-secondary);
    margin-bottom: var(--app-spacing-md);
    line-height: 1.5;
  }
}

// Table styles
.data-table {
  .el-table__header {
    th {
      background: var(--el-fill-color-lighter);
      font-weight: 600;
    }
  }

  .el-table__row {
    &:hover {
      background: var(--el-fill-color-light);
    }
  }
}

// Button groups
.button-group {
  display: flex;
  gap: var(--app-spacing-sm);

  &.button-group-right {
    justify-content: flex-end;
  }

  &.button-group-center {
    justify-content: center;
  }
}

// Responsive utilities
@media (max-width: 768px) {
  .app-sidebar {
    width: 100%;
    position: fixed;
    top: var(--app-header-height);
    left: 0;
    height: calc(100vh - var(--app-header-height));
    transform: translateX(-100%);
    transition: transform 0.3s ease;

    &.sidebar-open {
      transform: translateX(0);
    }
  }

  .app-main {
    padding: var(--app-spacing-md);
  }

  .button-group {
    flex-direction: column;

    .el-button {
      width: 100%;
    }
  }
}

// Print styles
@media print {
  .app-header,
  .app-sidebar,
  .el-button,
  .el-pagination {
    display: none !important;
  }

  .app-main {
    padding: 0;
    background: white;
  }
}
