// Custom CSS variables for the application

:root {
  // Layout
  --app-sidebar-width: 240px;
  --app-header-height: 60px;
  --app-border-radius: 8px;
  
  // Spacing
  --app-spacing-xs: 4px;
  --app-spacing-sm: 8px;
  --app-spacing-md: 16px;
  --app-spacing-lg: 24px;
  --app-spacing-xl: 32px;
  
  // Shadows
  --app-shadow-light: 0 2px 4px rgba(0, 0, 0, 0.1);
  --app-shadow-medium: 0 4px 8px rgba(0, 0, 0, 0.15);
  --app-shadow-heavy: 0 8px 16px rgba(0, 0, 0, 0.2);
  
  // Status colors
  --status-success: #67c23a;
  --status-warning: #e6a23c;
  --status-danger: #f56c6c;
  --status-info: #409eff;
  
  // Task status colors
  --task-active: var(--status-success);
  --task-inactive: #909399;
  --task-paused: var(--status-warning);
  
  // Log level colors
  --log-error: var(--status-danger);
  --log-warn: var(--status-warning);
  --log-info: var(--status-info);
  --log-debug: #909399;
  
  // Chart colors
  --chart-primary: #409eff;
  --chart-success: #67c23a;
  --chart-warning: #e6a23c;
  --chart-danger: #f56c6c;
  --chart-info: #909399;
}

// Dark mode variables
[data-theme="dark"] {
  --app-shadow-light: 0 2px 4px rgba(0, 0, 0, 0.3);
  --app-shadow-medium: 0 4px 8px rgba(0, 0, 0, 0.4);
  --app-shadow-heavy: 0 8px 16px rgba(0, 0, 0, 0.5);
}
