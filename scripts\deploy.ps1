# Deployment script for Cron Task Manager
# This script deploys both frontend and backend to Cloudflare

param(
    [string]$Environment = "production",
    [switch]$SkipBuild = $false,
    [switch]$FrontendOnly = $false,
    [switch]$BackendOnly = $false
)

Write-Host "🚀 Starting deployment for environment: $Environment" -ForegroundColor Green

# Set error action preference
$ErrorActionPreference = "Stop"

# Get script directory
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$RootDir = Split-Path -Parent $ScriptDir

try {
    # Deploy backend (Cloudflare Worker)
    if (-not $FrontendOnly) {
        Write-Host "📦 Deploying backend (Cloudflare Worker)..." -ForegroundColor Yellow
        
        Set-Location "$RootDir\worker"
        
        # Install dependencies if node_modules doesn't exist
        if (-not (Test-Path "node_modules")) {
            Write-Host "Installing backend dependencies..." -ForegroundColor Blue
            npm install
        }
        
        # Deploy worker
        if ($Environment -eq "production") {
            npx wrangler deploy
        } else {
            npx wrangler deploy --env $Environment
        }
        
        Write-Host "✅ Backend deployed successfully!" -ForegroundColor Green
    }
    
    # Deploy frontend (Cloudflare Pages)
    if (-not $BackendOnly) {
        Write-Host "🎨 Deploying frontend (Cloudflare Pages)..." -ForegroundColor Yellow
        
        Set-Location "$RootDir\frontend"
        
        # Install dependencies if node_modules doesn't exist
        if (-not (Test-Path "node_modules")) {
            Write-Host "Installing frontend dependencies..." -ForegroundColor Blue
            npm install
        }
        
        # Build frontend if not skipping
        if (-not $SkipBuild) {
            Write-Host "Building frontend..." -ForegroundColor Blue
            npm run build
        }
        
        # Deploy to Cloudflare Pages
        Write-Host "Deploying to Cloudflare Pages..." -ForegroundColor Blue
        npx wrangler pages deploy dist --project-name cron-task-frontend
        
        Write-Host "✅ Frontend deployed successfully!" -ForegroundColor Green
    }
    
    Write-Host "🎉 Deployment completed successfully!" -ForegroundColor Green
    
} catch {
    Write-Host "❌ Deployment failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
} finally {
    # Return to original directory
    Set-Location $RootDir
}

Write-Host ""
Write-Host "📋 Post-deployment checklist:" -ForegroundColor Cyan
Write-Host "1. Verify worker is running at your worker URL" -ForegroundColor White
Write-Host "2. Check frontend is accessible at your Pages URL" -ForegroundColor White
Write-Host "3. Test login functionality" -ForegroundColor White
Write-Host "4. Verify task creation and execution" -ForegroundColor White
Write-Host "5. Check logs and monitoring" -ForegroundColor White
