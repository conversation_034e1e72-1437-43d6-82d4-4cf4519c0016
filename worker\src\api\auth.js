/**
 * Authentication API routes
 */

import { Hono } from 'hono';
import { JWTUtil, PasswordUtil, RateLimiter } from '../utils/jwt.js';
import { ValidationError, RateLimitError } from '../middleware/error.js';

const app = new Hono();

/**
 * POST /login - User login
 */
app.post('/login', async (c) => {
  try {
    // Get request data
    const { password } = await c.req.json();
    
    if (!password) {
      throw new ValidationError('Password is required');
    }

    // Get client IP for rate limiting
    const clientIP = c.req.header('CF-Connecting-IP') || 
                     c.req.header('X-Forwarded-For') || 
                     c.req.header('X-Real-IP') || 
                     'unknown';

    // Check rate limiting
    const rateLimiter = new RateLimiter(c.env.TASKS_KV);
    const rateLimit = await rateLimiter.checkRateLimit(clientIP, 5, 15 * 60 * 1000); // 5 attempts per 15 minutes
    
    if (!rateLimit.allowed) {
      const resetTime = new Date(rateLimit.resetTime);
      throw new RateLimitError(`Too many login attempts. Try again after ${resetTime.toISOString()}`);
    }

    // Verify password
    const storedPassword = c.env.PASSWORD;
    if (!storedPassword) {
      console.error('PASSWORD environment variable not configured');
      return c.json({
        error: 'Authentication not configured',
        message: 'Server configuration error'
      }, 500);
    }

    const passwordUtil = new PasswordUtil();
    const isValid = passwordUtil.verifySimplePassword(password, storedPassword);
    
    if (!isValid) {
      return c.json({
        error: 'Unauthorized',
        message: 'Invalid password'
      }, 401);
    }

    // Reset rate limit on successful login
    await rateLimiter.resetRateLimit(clientIP);

    // Generate JWT token
    const jwtSecret = c.env.JWT_SECRET;
    if (!jwtSecret) {
      console.error('JWT_SECRET environment variable not configured');
      return c.json({
        error: 'Authentication not configured',
        message: 'Server configuration error'
      }, 500);
    }

    const jwtUtil = new JWTUtil(jwtSecret);
    const token = await jwtUtil.createLoginToken('admin', {
      ip: clientIP,
      userAgent: c.req.header('User-Agent') || 'unknown'
    });

    // Return success response
    return c.json({
      success: true,
      token,
      expiresIn: '24h',
      user: {
        id: 'admin',
        role: 'admin'
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    if (error instanceof ValidationError || error instanceof RateLimitError) {
      throw error;
    }
    
    console.error('Login error:', error);
    return c.json({
      error: 'Internal Server Error',
      message: 'Login failed'
    }, 500);
  }
});

/**
 * POST /verify - Verify token
 */
app.post('/verify', async (c) => {
  try {
    // Get token from request
    const authHeader = c.req.header('Authorization');
    if (!authHeader) {
      return c.json({
        error: 'Unauthorized',
        message: 'Authorization header is required'
      }, 401);
    }

    const jwtSecret = c.env.JWT_SECRET;
    if (!jwtSecret) {
      return c.json({
        error: 'Authentication not configured',
        message: 'Server configuration error'
      }, 500);
    }

    const jwtUtil = new JWTUtil(jwtSecret);
    const token = jwtUtil.extractTokenFromHeader(authHeader);
    
    if (!token) {
      return c.json({
        error: 'Unauthorized',
        message: 'Invalid authorization header format'
      }, 401);
    }

    // Verify token
    const payload = await jwtUtil.verifyLoginToken(token);
    
    return c.json({
      valid: true,
      user: {
        id: payload.userId,
        role: 'admin'
      },
      expiresAt: new Date(payload.exp * 1000).toISOString(),
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    return c.json({
      valid: false,
      error: 'Invalid or expired token',
      timestamp: new Date().toISOString()
    }, 401);
  }
});

/**
 * POST /logout - User logout (optional, mainly for client-side cleanup)
 */
app.post('/logout', async (c) => {
  // Since we're using stateless JWT, logout is mainly client-side
  // In a more complex setup, you might maintain a blacklist of tokens
  
  return c.json({
    success: true,
    message: 'Logged out successfully',
    timestamp: new Date().toISOString()
  });
});

/**
 * GET /status - Authentication status
 */
app.get('/status', async (c) => {
  return c.json({
    authenticationEnabled: !!(c.env.PASSWORD && c.env.JWT_SECRET),
    rateLimitingEnabled: true,
    timestamp: new Date().toISOString()
  });
});

export default app;
