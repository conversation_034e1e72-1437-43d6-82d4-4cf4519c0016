export default {
  // 通用
  common: {
    confirm: '确认',
    cancel: '取消',
    save: '保存',
    delete: '删除',
    edit: '编辑',
    add: '添加',
    create: '创建',
    update: '更新',
    search: '搜索',
    filter: '筛选',
    refresh: '刷新',
    loading: '加载中...',
    noData: '暂无数据',
    success: '成功',
    error: '错误',
    warning: '警告',
    info: '信息',
    yes: '是',
    no: '否',
    close: '关闭',
    back: '返回',
    next: '下一步',
    previous: '上一步',
    submit: '提交',
    reset: '重置',
    clear: '清空',
    clearFilters: '清空筛选',
    actions: '操作',
    status: '状态',
    type: '类型',
    name: '名称',
    description: '描述',
    createdAt: '创建时间',
    updatedAt: '更新时间',
    export: '导出',
    clearAll: '清空全部'
  },

  // 应用标题
  app: {
    title: '定时任务管理器',
    subtitle: '管理您的定时任务'
  },

  // 登录页面
  login: {
    title: '定时任务管理器',
    subtitle: '登录以管理您的定时任务',
    password: '密码',
    passwordPlaceholder: '请输入密码',
    signIn: '登录',
    signingIn: '登录中...',
    loginSuccess: '登录成功！',
    passwordRequired: '密码不能为空',
    passwordMinLength: '密码不能为空',
    infoText: '请输入管理员密码以访问控制台'
  },

  // 导航菜单
  nav: {
    dashboard: '仪表板',
    tasks: '任务',
    logs: '日志',
    cron: 'Cron管理',
    profile: '个人资料',
    settings: '设置',
    logout: '退出登录'
  },

  // 仪表板
  dashboard: {
    title: '仪表板',
    overview: {
      totalTasks: '总任务数',
      activeTasks: '活跃任务',
      healthScore: '健康评分',
      recentFailures: '最近失败'
    },
    upcomingTasks: {
      title: '即将执行的任务',
      noTasks: '没有即将执行的任务',
      noTasksDesc: '所有任务都处于非活跃状态或未安排执行',
      nextRun: '下次执行',
      timeUntil: '剩余时间'
    },
    recentFailures: {
      title: '最近失败',
      noFailures: '没有最近失败',
      noFailuresDesc: '所有任务都在正常运行',
      retry: '重试'
    },
    systemStatus: {
      title: '系统状态',
      scheduler: '调度器',
      lastRun: '最后运行',
      uptime: '运行时间',
      totalRuns: '总运行次数',
      running: '运行中',
      stalled: '停滞',
      delayed: '延迟',
      notRunning: '未运行'
    },
    tasksByType: {
      title: '按类型分组的任务',
      noTasks: '没有任务',
      noTasksDesc: '创建您的第一个任务以查看统计信息'
    },
    alerts: {
      title: '系统警报'
    },
    loadingText: '正在加载仪表板数据...',
    errorTitle: '加载仪表板失败',
    errorDesc: '请尝试刷新页面',
    retry: '重试'
  },

  // 任务管理
  tasks: {
    title: '任务',
    createTask: '创建任务',
    createFirstTask: '创建第一个任务',
    noTasks: '没有找到任务',
    noTasksDesc: '创建您的第一个任务开始使用',
    noTasksFiltered: '尝试调整您的筛选条件',
    loadingText: '正在加载任务...',
    searchPlaceholder: '搜索任务...',
    filterByStatus: '按状态筛选',
    filterByType: '按类型筛选',
    table: {
      name: '名称',
      type: '类型',
      status: '状态',
      schedule: '计划',
      lastRun: '最后运行',
      nextRun: '下次运行',
      actions: '操作'
    },
    status: {
      active: '活跃',
      inactive: '非活跃',
      paused: '暂停',
      running: '运行中',
      failed: '失败',
      success: '成功'
    },
    actions: {
      run: '立即运行',
      pause: '暂停',
      resume: '恢复',
      edit: '编辑',
      delete: '删除',
      view: '查看',
      duplicate: '复制'
    },
    form: {
      basicInfo: '基本信息',
      name: '任务名称',
      namePlaceholder: '请输入任务名称',
      nameRequired: '任务名称不能为空',
      description: '任务描述',
      descriptionPlaceholder: '请输入任务描述',
      type: '任务类型',
      typePlaceholder: '请选择任务类型',
      typeRequired: '请选择任务类型',
      schedule: '执行计划',
      schedulePlaceholder: '请输入Cron表达式',
      scheduleRequired: '执行计划不能为空',
      scheduleInvalid: '无效的Cron表达式',
      enabled: '启用任务',
      configuration: '任务配置',
      save: '保存任务',
      cancel: '取消'
    },
    types: {
      http_request: 'HTTP请求',
      webhook: 'Webhook',
      email: '邮件发送',
      database_cleanup: '数据库清理',
      file_backup: '文件备份',
      system_check: '系统检查'
    }
  },

  // 日志
  logs: {
    title: '日志',
    noLogs: '没有日志',
    noLogsDesc: '还没有任务执行日志',
    loadingText: '正在加载日志...',
    filterByTask: '按任务筛选',
    filterByStatus: '按状态筛选',
    table: {
      task: '任务',
      status: '状态',
      startTime: '开始时间',
      duration: '持续时间',
      message: '消息',
      actions: '操作'
    },
    status: {
      success: '成功',
      failed: '失败',
      running: '运行中',
      cancelled: '已取消'
    },
    actions: {
      view: '查看详情',
      retry: '重试'
    },
    details: {
      title: '日志详情',
      taskName: '任务名称',
      executionId: '执行ID',
      status: '状态',
      startTime: '开始时间',
      endTime: '结束时间',
      duration: '持续时间',
      output: '输出',
      error: '错误信息'
    }
  },

  // 时间格式化
  time: {
    now: '现在',
    never: '从未',
    notScheduled: '未安排',
    inMinutes: '{count}分钟后',
    inHours: '{count}小时后',
    inDays: '{count}天后',
    lessThanMinute: '不到1分钟',
    unknown: '未知'
  },

  // 消息提示
  messages: {
    taskCreated: '任务创建成功',
    taskUpdated: '任务更新成功',
    taskDeleted: '任务删除成功',
    taskStarted: '任务启动成功',
    taskPaused: '任务暂停成功',
    taskResumed: '任务恢复成功',
    operationFailed: '操作失败',
    confirmDelete: '确认删除此任务吗？',
    confirmDeleteDesc: '此操作不可撤销',
    networkError: '网络错误，请稍后重试',
    unauthorized: '未授权，请重新登录',
    serverError: '服务器错误，请联系管理员'
  },

  // 404页面
  notFound: {
    title: '页面未找到',
    description: '您访问的页面不存在或已被移动。',
    goToDashboard: '前往仪表板',
    goBack: '返回',
    helpfulLinksTitle: '您可能在寻找：'
  }
}
