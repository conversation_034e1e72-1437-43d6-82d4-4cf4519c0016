{"name": "self-scheduling-tasks", "version": "1.0.0", "description": "Cloudflare Durable Objects自调度任务系统", "main": "src/index.js", "scripts": {"dev": "wrangler dev", "deploy": "wrangler deploy", "deploy:dev": "wrangler deploy --env development", "deploy:prod": "wrangler deploy --env production", "tail": "wrangler tail", "kv:create": "wrangler kv:namespace create TASKS_KV", "kv:list": "wrangler kv:key list --binding TASKS_KV", "test": "vitest", "lint": "eslint src --ext .js", "format": "prettier --write src/**/*.js"}, "dependencies": {"hono": "^4.0.0"}, "devDependencies": {"@cloudflare/workers-types": "^4.20240208.0", "wrangler": "^3.28.0", "vitest": "^1.2.0", "eslint": "^8.56.0", "prettier": "^3.2.0"}, "keywords": ["cloudflare", "workers", "durable-objects", "scheduler"], "author": "", "license": "MIT"}