# Cron Task 定时任务管理系统

基于 Cloudflare Workers 和 Vue3 的现代化定时任务管理系统，采用调度器-Actor 模式实现高可用的任务调度和执行。

## 系统特性

- 🚀 **高性能调度**：基于 Cloudflare Workers 的全球分布式调度
- 🔄 **解耦架构**：调度器与执行器分离，使用 Durable Objects 实现状态持久化
- 🎯 **精确执行**：支持 Cron 表达式的精确时间调度
- 🛡️ **容错机制**：内置重试逻辑和失败处理
- 📊 **实时监控**：任务状态监控和执行日志查看
- 🎨 **现代界面**：基于 Vue3 + Element Plus 的响应式管理界面
- ⚡ **动态调度**：智能 Cron 触发器优化，根据任务负载自动调整调度频率
- 🔧 **灵活配置**：支持手动和自动触发器管理，提供完整的配置验证

## 技术栈

### 前端
- Vue 3 (Composition API)
- Vue Router
- Pinia (状态管理)
- Element Plus (UI 组件库)
- TanStack Query (数据获取)
- Vite (构建工具)

### 后端
- Cloudflare Workers
- Hono (Web 框架)
- Durable Objects (状态管理)
- Cloudflare KV (数据存储)
- JWT (身份认证)

## 项目结构

```
cron-task/
├── frontend/                          # 前端 Vue3 应用
│   ├── public/                        # 静态资源
│   ├── src/                           # 源代码目录
│   │   ├── components/                # 可复用组件
│   │   ├── views/                     # 页面组件
│   │   ├── stores/                    # Pinia状态管理
│   │   ├── api/                       # API接口封装
│   │   ├── utils/                     # 工具函数
│   │   └── router/                    # 路由配置
│   ├── package.json                   # 前端依赖配置
│   └── vite.config.js                 # Vite构建配置
├── worker/                            # Cloudflare Worker 后端
│   ├── src/                           # 源代码目录
│   │   ├── handlers/                  # 事件处理器
│   │   ├── durable-objects/           # Durable Objects 定义
│   │   ├── api/                       # API路由
│   │   ├── executors/                 # 任务执行器模块
│   │   ├── middleware/                # 中间件
│   │   ├── services/                  # 业务服务层
│   │   ├── utils/                     # 工具函数
│   │   └── schemas/                   # 数据模式定义
│   ├── wrangler.toml                  # Cloudflare Worker配置
│   └── package.json                   # 后端依赖配置
├── docs/                              # 项目文档
├── scripts/                           # 构建和部署脚本
└── README.md                          # 项目说明文档
```

## 核心架构

系统采用调度器-Actor（Scheduler-Actor）模式：

1. **调度器**：通过 Cron 触发器定期查询待执行任务，并向对应的 Durable Object 发送执行指令
2. **任务执行器**：每个任务对应一个 TaskExecutorDO 实例，负责具体的任务执行、重试和状态管理
3. **API 网关**：处理前端请求，提供任务管理、监控等功能的 RESTful API

## 快速开始

### 环境要求

- Node.js 18+
- npm/pnpm
- Cloudflare 账户
- Wrangler CLI (`npm install -g wrangler`)

### 安装依赖

```bash
# 使用开发脚本安装所有依赖
.\scripts\dev.ps1 -Install

# 或者手动安装
cd frontend && npm install
cd ../worker && npm install
```

### 开发环境

```bash
# 启动完整开发环境（前端 + 后端）
.\scripts\dev.ps1

# 或者分别启动
.\scripts\dev.ps1 -FrontendOnly  # 仅前端
.\scripts\dev.ps1 -BackendOnly   # 仅后端
```

开发服务器地址：
- 前端：http://localhost:5173
- 后端：http://localhost:8787

### 部署

```bash
# 完整部署（推荐）
.\scripts\deploy.ps1

# 分别部署
.\scripts\deploy.ps1 -FrontendOnly  # 仅前端
.\scripts\deploy.ps1 -BackendOnly   # 仅后端
```

详细部署说明请参考 [部署文档](docs/DEPLOYMENT.md)。

### 动态 Cron 配置（可选）

如需启用动态 Cron 触发器优化功能，请配置以下环境变量：

```bash
# 在 worker/.env 文件中添加
CLOUDFLARE_API_TOKEN=your_api_token_here
ACCOUNT_ID=your_account_id_here
ENABLE_DYNAMIC_CRON=true
CRON_UPDATE_MIN_INTERVAL=300000
```

详细配置说明请参考 [动态 Cron 设置指南](DYNAMIC_CRON_SETUP.md)。

## 功能特性

### 🎯 任务管理
- **多种任务类型**：HTTP 请求、Webhook、邮件通知等
- **灵活调度**：支持标准 Cron 表达式
- **状态管理**：活跃、暂停、停用状态控制
- **批量操作**：支持任务的批量启用/禁用/删除

### 🔄 执行引擎
- **解耦架构**：调度器与执行器分离设计
- **并发控制**：防止同一任务重复执行
- **重试机制**：支持多种重试策略（固定、线性、指数退避）
- **超时控制**：可配置的任务执行超时

### 📊 监控告警
- **实时仪表盘**：任务状态、执行统计、系统健康度
- **执行日志**：详细的成功/失败日志记录
- **智能告警**：基于失败率和系统状态的自动告警
- **性能指标**：执行时间、成功率等关键指标

### 🛡️ 安全特性
- **JWT 认证**：安全的身份验证机制
- **速率限制**：防止暴力破解和滥用
- **CORS 配置**：跨域请求安全控制
- **输入验证**：严格的数据验证和清理

### 🌐 现代化界面
- **响应式设计**：支持桌面和移动设备
- **实时更新**：基于 TanStack Query 的数据同步
- **动态表单**：根据任务类型自动生成配置界面

### ⚡ 动态 Cron 调度（新功能）
- **智能优化**：根据活跃任务自动计算最优调度频率
- **实时更新**：通过 Cloudflare API 动态修改 Worker Cron 触发器
- **系统感知**：基于系统负载和错误率调整调度策略
- **手动控制**：提供 Web 界面和 API 进行手动触发器管理
- **历史追踪**：完整的触发器更新历史和性能指标
- **配置验证**：全面的配置验证和错误处理机制
- **主题支持**：明暗主题切换

## 配置说明

### 环境变量

**后端 (Worker)**
```bash
# 必需配置
PASSWORD=your_admin_password
JWT_SECRET=your_jwt_secret_key

# 可选配置
CLOUDFLARE_API_TOKEN=your_api_token
ACCOUNT_ID=your_account_id
EMAIL_SERVICE_URL=your_email_service_url
EMAIL_API_KEY=your_email_api_key
```

**前端**
```bash
# API 基础 URL
VITE_API_BASE_URL=https://your-worker.workers.dev/api

# 应用配置
VITE_APP_TITLE=Cron Task Manager
VITE_ENABLE_DEBUG=false
```

### 任务类型配置

系统支持以下任务类型：

1. **HTTP 请求**：发送 HTTP 请求到指定 URL
2. **Webhook**：发送带签名的 Webhook 通知
3. **邮件通知**：发送邮件（需配置邮件服务）

每种任务类型都有对应的配置模式，前端会自动生成相应的配置表单。

## API 文档

完整的 API 文档请参考 [API.md](docs/API.md)。

### 主要端点

**认证**
- `POST /api/auth/login` - 用户登录
- `POST /api/auth/verify` - 验证令牌
- `GET /api/auth/status` - 获取认证状态

**任务管理**
- `GET /api/tasks` - 获取任务列表
- `POST /api/tasks` - 创建任务
- `PUT /api/tasks/:id` - 更新任务
- `DELETE /api/tasks/:id` - 删除任务
- `POST /api/tasks/:id/run` - 手动触发任务
- `GET /api/tasks/types` - 获取任务类型

**监控**
- `GET /api/dashboard` - 获取仪表盘数据
- `GET /api/dashboard/stats` - 获取详细统计
- `GET /api/logs` - 获取执行日志
- `GET /api/logs/stats` - 获取日志统计

**Cron 触发器管理**
- `GET /api/cron/status` - 获取触发器状态
- `GET /api/cron/preview` - 预览优化建议
- `POST /api/cron/update` - 手动更新触发器
- `POST /api/cron/optimize` - 强制优化触发器
- `GET /api/cron/history` - 获取更新历史
- `GET /api/cron/metrics` - 获取性能指标

## 开发指南

### 添加新的任务类型

1. 在 `worker/src/executors/` 目录创建新的执行器文件
2. 实现标准的执行器接口
3. 在 `worker/src/executors/registry.js` 中注册新执行器
4. 前端会自动识别并生成配置表单

### 自定义中间件

在 `worker/src/middleware/` 目录添加新的中间件，并在路由中使用。

### 扩展前端组件

在 `frontend/src/components/` 目录添加新组件，使用 Element Plus 组件库。

## 故障排除

### 常见问题

1. **Worker 部署失败**
   - 检查 KV 命名空间配置
   - 验证环境变量设置
   - 查看 Wrangler 日志

2. **前端无法连接后端**
   - 检查 API 基础 URL 配置
   - 验证 CORS 设置
   - 确认 Worker 正常运行

3. **任务不执行**
   - 检查 Cron 表达式格式
   - 验证任务状态为 "active"
   - 查看 Worker 日志

4. **认证失败**
   - 确认密码和 JWT 密钥设置
   - 检查令牌是否过期
   - 验证请求头格式

5. **动态 Cron 更新失败**
   - 验证 Cloudflare API Token 权限
   - 检查 Account ID 配置
   - 确认 Worker 名称正确
   - 查看更新频率限制

6. **触发器优化不生效**
   - 确认 `ENABLE_DYNAMIC_CRON=true`
   - 检查系统健康状况
   - 验证活跃任务数量
   - 查看优化算法日志

### 调试工具

```bash
# 查看 Worker 实时日志
wrangler tail

# 本地调试 Worker
cd worker && npm run dev

# 本地调试前端
cd frontend && npm run dev
```

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 许可证

MIT License - 详见 [LICENSE](LICENSE) 文件。

## 致谢

- [Cloudflare Workers](https://workers.cloudflare.com/) - 无服务器计算平台
- [Vue 3](https://vuejs.org/) - 渐进式 JavaScript 框架
- [Element Plus](https://element-plus.org/) - Vue 3 组件库
- [Hono](https://hono.dev/) - 轻量级 Web 框架
