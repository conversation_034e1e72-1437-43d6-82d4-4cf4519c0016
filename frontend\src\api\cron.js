/**
 * <PERSON>ron Triggers API Client
 */

import apiClient from './client.js';

/**
 * Gets current cron trigger status and configuration
 * @returns {Promise<Object>} - Cron status data
 */
export async function getCronStatus() {
  const response = await apiClient.get('/cron/status');
  return response.data;
}

/**
 * Gets cron trigger update history
 * @returns {Promise<Array>} - Update history
 */
export async function getCronHistory() {
  const response = await apiClient.get('/cron/history');
  return response.data;
}

/**
 * Manually updates cron triggers
 * @param {Array} triggers - Array of cron expressions
 * @param {string} reason - Reason for the update
 * @returns {Promise<Object>} - Update result
 */
export async function updateCronTriggers(triggers, reason = 'manual') {
  const response = await apiClient.post('/cron/update', {
    triggers,
    reason
  });
  return response.data;
}

/**
 * Forces cron optimization based on current tasks
 * @returns {Promise<Object>} - Optimization result
 */
export async function optimizeCronTriggers() {
  const response = await apiClient.post('/cron/optimize');
  return response.data;
}

/**
 * Previews optimal cron expression for current tasks
 * @returns {Promise<Object>} - Preview data
 */
export async function previewOptimalCron() {
  const response = await apiClient.get('/cron/preview');
  return response.data;
}

/**
 * Gets cron update metrics and statistics
 * @returns {Promise<Object>} - Metrics data
 */
export async function getCronMetrics() {
  const response = await apiClient.get('/cron/metrics');
  return response.data;
}

/**
 * Validates cron expressions without updating
 * @param {Array} triggers - Array of cron expressions to validate
 * @returns {Promise<Object>} - Validation results
 */
export async function validateCronExpressions(triggers) {
  const response = await apiClient.post('/cron/validate', {
    triggers
  });
  return response.data;
}

/**
 * Parses a cron expression and returns human-readable description
 * @param {string} cronExpression - Cron expression to parse
 * @returns {string} - Human-readable description
 */
export function describeCronExpression(cronExpression) {
  try {
    const parts = cronExpression.split(' ');
    if (parts.length !== 5) {
      return 'Invalid cron expression';
    }

    const [minute, hour, day, month, weekday] = parts;

    // Common patterns
    const patterns = {
      '*/1 * * * *': 'Every minute',
      '*/2 * * * *': 'Every 2 minutes',
      '*/3 * * * *': 'Every 3 minutes',
      '*/5 * * * *': 'Every 5 minutes',
      '*/10 * * * *': 'Every 10 minutes',
      '*/15 * * * *': 'Every 15 minutes',
      '*/30 * * * *': 'Every 30 minutes',
      '0 */1 * * *': 'Every hour',
      '0 */2 * * *': 'Every 2 hours',
      '0 */6 * * *': 'Every 6 hours',
      '0 */12 * * *': 'Every 12 hours',
      '0 0 * * *': 'Daily at midnight',
      '0 12 * * *': 'Daily at noon',
      '0 0 * * 0': 'Weekly on Sunday at midnight',
      '0 0 1 * *': 'Monthly on the 1st at midnight'
    };

    if (patterns[cronExpression]) {
      return patterns[cronExpression];
    }

    // Generic description
    let description = 'At ';

    if (minute === '*') {
      description += 'every minute';
    } else if (minute.startsWith('*/')) {
      description += `every ${minute.slice(2)} minutes`;
    } else {
      description += `minute ${minute}`;
    }

    if (hour !== '*') {
      if (hour.startsWith('*/')) {
        description += ` of every ${hour.slice(2)} hours`;
      } else {
        description += ` of hour ${hour}`;
      }
    }

    if (day !== '*') {
      description += ` on day ${day}`;
    }

    if (month !== '*') {
      description += ` of month ${month}`;
    }

    if (weekday !== '*') {
      const weekdays = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
      if (weekdays[parseInt(weekday)]) {
        description += ` on ${weekdays[parseInt(weekday)]}`;
      }
    }

    return description;
  } catch (error) {
    return 'Invalid cron expression';
  }
}

/**
 * Validates a single cron expression format
 * @param {string} cronExpression - Cron expression to validate
 * @returns {Object} - Validation result
 */
export function validateCronFormat(cronExpression) {
  if (!cronExpression || typeof cronExpression !== 'string') {
    return {
      valid: false,
      error: 'Cron expression must be a non-empty string'
    };
  }

  const parts = cronExpression.trim().split(/\s+/);
  if (parts.length !== 5) {
    return {
      valid: false,
      error: 'Cron expression must have exactly 5 fields (minute hour day month weekday)'
    };
  }

  // Basic field validation
  const [minute, hour, day, month, weekday] = parts;

  const validations = [
    { field: minute, name: 'minute', min: 0, max: 59 },
    { field: hour, name: 'hour', min: 0, max: 23 },
    { field: day, name: 'day', min: 1, max: 31 },
    { field: month, name: 'month', min: 1, max: 12 },
    { field: weekday, name: 'weekday', min: 0, max: 7 }
  ];

  for (const validation of validations) {
    const result = validateCronField(validation.field, validation.name, validation.min, validation.max);
    if (!result.valid) {
      return result;
    }
  }

  return { valid: true };
}

/**
 * Validates a single cron field
 * @param {string} field - Field value
 * @param {string} name - Field name
 * @param {number} min - Minimum value
 * @param {number} max - Maximum value
 * @returns {Object} - Validation result
 */
function validateCronField(field, name, min, max) {
  if (field === '*') {
    return { valid: true };
  }

  // Handle step values (*/n)
  if (field.startsWith('*/')) {
    const step = parseInt(field.slice(2));
    if (isNaN(step) || step <= 0 || step > max) {
      return {
        valid: false,
        error: `Invalid step value in ${name} field: ${field}`
      };
    }
    return { valid: true };
  }

  // Handle ranges (n-m)
  if (field.includes('-')) {
    const [start, end] = field.split('-').map(n => parseInt(n));
    if (isNaN(start) || isNaN(end) || start < min || end > max || start > end) {
      return {
        valid: false,
        error: `Invalid range in ${name} field: ${field}`
      };
    }
    return { valid: true };
  }

  // Handle lists (n,m,o)
  if (field.includes(',')) {
    const values = field.split(',').map(n => parseInt(n));
    for (const value of values) {
      if (isNaN(value) || value < min || value > max) {
        return {
          valid: false,
          error: `Invalid value in ${name} field: ${field}`
        };
      }
    }
    return { valid: true };
  }

  // Handle single values
  const value = parseInt(field);
  if (isNaN(value) || value < min || value > max) {
    return {
      valid: false,
      error: `Invalid ${name} value: ${field} (must be between ${min} and ${max})`
    };
  }

  return { valid: true };
}

/**
 * Gets the frequency category of a cron expression
 * @param {string} cronExpression - Cron expression
 * @returns {string} - Frequency category
 */
export function getCronFrequencyCategory(cronExpression) {
  const parts = cronExpression.split(' ');
  if (parts.length !== 5) {
    return 'invalid';
  }

  const [minute, hour] = parts;

  if (minute.includes('*') && hour === '*') {
    return 'minutely';
  } else if (hour.includes('*')) {
    return 'hourly';
  } else {
    return 'daily_or_less';
  }
}

/**
 * Estimates the next few execution times for a cron expression
 * @param {string} cronExpression - Cron expression
 * @param {number} count - Number of times to calculate
 * @returns {Array} - Array of estimated execution times
 */
export function estimateNextExecutions(cronExpression, count = 5) {
  // This is a simplified estimation - in a real implementation,
  // you might want to use a proper cron parser library
  const times = [];
  const now = new Date();

  try {
    // For demonstration, we'll just show some estimated times
    // In a real implementation, use a library like cron-parser
    for (let i = 0; i < count; i++) {
      const time = new Date(now.getTime() + (i + 1) * 60000); // Rough estimation
      times.push(time.toISOString());
    }
  } catch (error) {
    console.error('Error estimating execution times:', error);
  }

  return times;
}
