import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

// Import views
import Layout from '@/components/Layout.vue'
import Login from '@/views/Login.vue'
import Dashboard from '@/views/Dashboard.vue'
import Tasks from '@/views/Tasks.vue'
import Logs from '@/views/Logs.vue'
import CronManagement from '@/views/CronManagement.vue'
import NotFound from '@/views/NotFound.vue'

const routes = [
  {
    path: '/login',
    name: 'Login',
    component: Login,
    meta: {
      requiresAuth: false,
      title: 'Login'
    }
  },
  {
    path: '/',
    component: Layout,
    meta: {
      requiresAuth: true
    },
    children: [
      {
        path: '',
        redirect: '/dashboard'
      },
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: Dashboard,
        meta: {
          title: 'Dashboard',
          icon: 'Monitor'
        }
      },
      {
        path: 'tasks',
        name: 'Tasks',
        component: Tasks,
        meta: {
          title: 'Tasks',
          icon: 'Timer'
        }
      },
      {
        path: 'logs',
        name: 'Logs',
        component: Logs,
        meta: {
          title: 'Logs',
          icon: 'Document'
        }
      },
      {
        path: 'cron',
        name: 'CronManagement',
        component: CronManagement,
        meta: {
          title: 'Cron Management',
          icon: 'Setting'
        }
      }
    ]
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: NotFound,
    meta: {
      requiresAuth: false,
      title: 'Page Not Found'
    }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// Navigation guards
router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore()

  // Set page title
  document.title = to.meta.title ? `${to.meta.title} - 定时任务管理器` : '定时任务管理器'

  // Check if route requires authentication
  if (to.meta.requiresAuth !== false) {
    if (!authStore.isAuthenticated) {
      // Try to restore auth from localStorage
      await authStore.initializeAuth()

      if (!authStore.isAuthenticated) {
        next('/login')
        return
      }
    }
  } else {
    // If user is authenticated and trying to access login page, redirect to dashboard
    if (to.name === 'Login' && authStore.isAuthenticated) {
      next('/dashboard')
      return
    }
  }

  next()
})

export default router
