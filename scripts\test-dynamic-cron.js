#!/usr/bin/env node

/**
 * 动态 Cron 功能测试脚本
 * 用于验证动态 Cron 触发器功能是否正常工作
 */

const API_BASE = process.env.API_BASE || 'http://localhost:8787/api';
const USERNAME = process.env.ADMIN_USERNAME || 'admin';
const PASSWORD = process.env.ADMIN_PASSWORD || 'admin123';

let authToken = '';

/**
 * 发送 HTTP 请求
 */
async function request(method, endpoint, data = null) {
  const url = `${API_BASE}${endpoint}`;
  const options = {
    method,
    headers: {
      'Content-Type': 'application/json',
      ...(authToken && { 'Authorization': `Bearer ${authToken}` })
    }
  };

  if (data) {
    options.body = JSON.stringify(data);
  }

  try {
    const response = await fetch(url, options);
    const result = await response.json();
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${result.message || result.error}`);
    }
    
    return result;
  } catch (error) {
    console.error(`Request failed: ${method} ${endpoint}`, error.message);
    throw error;
  }
}

/**
 * 登录获取认证令牌
 */
async function login() {
  console.log('🔐 正在登录...');
  
  try {
    const result = await request('POST', '/auth/login', {
      username: USERNAME,
      password: PASSWORD
    });
    
    authToken = result.data.token;
    console.log('✅ 登录成功');
    return true;
  } catch (error) {
    console.error('❌ 登录失败:', error.message);
    return false;
  }
}

/**
 * 测试 Cron 状态获取
 */
async function testCronStatus() {
  console.log('\n📊 测试 Cron 状态获取...');
  
  try {
    const result = await request('GET', '/cron/status');
    console.log('✅ Cron 状态获取成功');
    console.log('   - 当前触发器数量:', result.data.currentTriggers?.length || 0);
    console.log('   - API 配置状态:', result.data.isConfigured ? '已配置' : '未配置');
    console.log('   - 动态更新状态:', result.data.dynamicUpdatesEnabled ? '启用' : '禁用');
    console.log('   - 更新权限:', result.data.canUpdateNow ? '可更新' : '限制中');
    return result.data;
  } catch (error) {
    console.error('❌ Cron 状态获取失败:', error.message);
    return null;
  }
}

/**
 * 测试配置验证
 */
async function testConfigValidation() {
  console.log('\n🔍 测试配置验证...');
  
  try {
    const result = await request('POST', '/cron/validate-config');
    console.log('✅ 配置验证成功');
    console.log('   - 验证结果:', result.data.validation.canProceed ? '通过' : '失败');
    console.log('   - 错误数量:', result.data.validation.errors.length);
    console.log('   - 警告数量:', result.data.validation.warnings.length);
    
    if (result.data.validation.errors.length > 0) {
      console.log('   - 错误详情:', result.data.validation.errors);
    }
    
    return result.data;
  } catch (error) {
    console.error('❌ 配置验证失败:', error.message);
    return null;
  }
}

/**
 * 测试优化预览
 */
async function testOptimizationPreview() {
  console.log('\n🎯 测试优化预览...');
  
  try {
    const result = await request('GET', '/cron/preview');
    console.log('✅ 优化预览成功');
    console.log('   - 当前主触发器:', result.data.currentMainTrigger);
    console.log('   - 建议触发器:', result.data.optimalCron);
    console.log('   - 是否已优化:', result.data.isOptimal ? '是' : '否');
    console.log('   - 活跃任务数:', result.data.activeTaskCount);
    console.log('   - 建议:', result.data.recommendation);
    return result.data;
  } catch (error) {
    console.error('❌ 优化预览失败:', error.message);
    return null;
  }
}

/**
 * 测试触发器验证
 */
async function testTriggerValidation() {
  console.log('\n✅ 测试触发器验证...');
  
  const testTriggers = [
    '*/5 * * * *',    // 有效
    '*/2 * * * *',    // 有效
    'invalid cron',   // 无效
    '*/60 * * * *'    // 无效（分钟超出范围）
  ];
  
  try {
    const result = await request('POST', '/cron/validate', {
      triggers: testTriggers
    });
    
    console.log('✅ 触发器验证成功');
    console.log('   - 总数:', result.data.summary.total);
    console.log('   - 有效:', result.data.summary.valid);
    console.log('   - 无效:', result.data.summary.invalid);
    
    result.data.results.forEach((validation, index) => {
      const status = validation.valid ? '✅' : '❌';
      console.log(`   ${status} 触发器 ${index + 1}: ${testTriggers[index]}`);
      if (!validation.valid) {
        console.log(`      错误: ${validation.error}`);
      }
    });
    
    return result.data;
  } catch (error) {
    console.error('❌ 触发器验证失败:', error.message);
    return null;
  }
}

/**
 * 测试更新历史
 */
async function testUpdateHistory() {
  console.log('\n📜 测试更新历史...');
  
  try {
    const result = await request('GET', '/cron/history');
    console.log('✅ 更新历史获取成功');
    console.log('   - 历史记录数:', result.data.length);
    
    if (result.data.length > 0) {
      const latest = result.data[0];
      console.log('   - 最新更新时间:', new Date(latest.timestamp).toLocaleString());
      console.log('   - 最新更新状态:', latest.success ? '成功' : '失败');
      console.log('   - 更新原因:', latest.reason);
    }
    
    return result.data;
  } catch (error) {
    console.error('❌ 更新历史获取失败:', error.message);
    return null;
  }
}

/**
 * 测试性能指标
 */
async function testMetrics() {
  console.log('\n📈 测试性能指标...');
  
  try {
    const result = await request('GET', '/cron/metrics');
    console.log('✅ 性能指标获取成功');
    console.log('   - 总更新次数:', result.data.statistics.totalUpdates);
    console.log('   - 成功更新次数:', result.data.statistics.successfulUpdates);
    console.log('   - 成功率:', (result.data.statistics.successRate * 100).toFixed(1) + '%');
    console.log('   - 最近24小时更新:', result.data.statistics.recentUpdates);
    
    return result.data;
  } catch (error) {
    console.error('❌ 性能指标获取失败:', error.message);
    return null;
  }
}

/**
 * 主测试函数
 */
async function runTests() {
  console.log('🚀 开始动态 Cron 功能测试\n');
  console.log('API 基础地址:', API_BASE);
  console.log('用户名:', USERNAME);
  
  // 登录
  const loginSuccess = await login();
  if (!loginSuccess) {
    console.log('\n❌ 测试终止：登录失败');
    process.exit(1);
  }
  
  // 运行测试
  const results = {
    status: await testCronStatus(),
    config: await testConfigValidation(),
    preview: await testOptimizationPreview(),
    validation: await testTriggerValidation(),
    history: await testUpdateHistory(),
    metrics: await testMetrics()
  };
  
  // 测试总结
  console.log('\n📋 测试总结:');
  const testCount = Object.keys(results).length;
  const successCount = Object.values(results).filter(result => result !== null).length;
  
  console.log(`   - 总测试数: ${testCount}`);
  console.log(`   - 成功测试: ${successCount}`);
  console.log(`   - 失败测试: ${testCount - successCount}`);
  console.log(`   - 成功率: ${(successCount / testCount * 100).toFixed(1)}%`);
  
  if (results.status && results.status.isConfigured) {
    console.log('\n🎉 动态 Cron 功能已正确配置并可用！');
  } else {
    console.log('\n⚠️  动态 Cron 功能未完全配置，请检查环境变量设置');
  }
  
  console.log('\n测试完成！');
}

// 运行测试
if (require.main === module) {
  runTests().catch(error => {
    console.error('\n💥 测试过程中发生错误:', error);
    process.exit(1);
  });
}

module.exports = {
  runTests,
  testCronStatus,
  testConfigValidation,
  testOptimizationPreview
};
