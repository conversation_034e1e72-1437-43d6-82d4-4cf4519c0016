import { Hono } from 'hono';

// Import API routes
import authRoutes from '../api/auth.js';
import taskRoutes from '../api/tasks.js';
import dashboardRoutes from '../api/dashboard.js';
import logRoutes from '../api/logs.js';
import cronRoutes from '../api/cron.js';

// Import middleware
import { authMiddleware } from '../middleware/auth.js';
import { errorHandler } from '../middleware/error.js';

const app = new Hono();

// Error handling middleware
app.use('*', errorHandler);

// Public routes (no authentication required)
app.route('/auth', authRoutes);

// Public task routes (no authentication required)
app.get('/tasks/types', async (c) => {
  // Import here to avoid circular dependency
  const { ExecutorRegistry } = await import('../executors/registry.js');

  try {
    const executors = ExecutorRegistry.getExecutorMetadata();

    return c.json({
      success: true,
      data: executors,
      count: executors.length,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Get task types error:', error);
    return c.json({
      error: 'Internal Server Error',
      message: 'Failed to retrieve task types'
    }, 500);
  }
});

// Protected routes (authentication required)
app.use('/tasks/*', authMiddleware);
app.use('/dashboard/*', authMiddleware);
app.use('/logs/*', authMiddleware);
app.use('/cron/*', authMiddleware);

app.route('/tasks', taskRoutes);
app.route('/dashboard', dashboardRoutes);
app.route('/logs', logRoutes);
app.route('/cron', cronRoutes);

// 404 handler
app.notFound((c) => {
  return c.json({
    error: 'Not Found',
    message: 'The requested endpoint does not exist'
  }, 404);
});

export { app as handleFetch };
