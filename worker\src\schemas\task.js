/**
 * Task data schema and validation
 */

/**
 * Task status enumeration
 */
export const TaskStatus = {
  ACTIVE: 'active',
  INACTIVE: 'inactive',
  PAUSED: 'paused'
};

/**
 * Task type enumeration
 */
export const TaskType = {
  HTTP_REQUEST: 'http_request',
  WEBHOOK: 'webhook',
  EMAIL: 'email',
  CUSTOM: 'custom'
};

/**
 * Retry strategy enumeration
 */
export const RetryStrategy = {
  FIXED: 'fixed',
  EXPONENTIAL: 'exponential',
  LINEAR: 'linear'
};

/**
 * Task schema definition
 */
export const TaskSchema = {
  id: 'string',              // UUID
  name: 'string',            // Task name
  description: 'string',     // Task description
  type: 'string',            // TaskType
  status: 'string',          // TaskStatus
  cron: 'string',            // Cron expression
  timezone: 'string',        // Timezone (default: UTC)
  config: 'object',          // Task-specific configuration
  retryConfig: {
    enabled: 'boolean',
    maxRetries: 'number',
    strategy: 'string',      // RetryStrategy
    baseDelay: 'number',     // Base delay in milliseconds
    maxDelay: 'number'       // Maximum delay in milliseconds
  },
  timeout: 'number',         // Timeout in milliseconds
  tags: 'array',             // Array of strings
  metadata: 'object',        // Additional metadata
  createdAt: 'string',       // ISO timestamp
  updatedAt: 'string',       // ISO timestamp
  lastRunAt: 'string',       // ISO timestamp
  nextRunAt: 'string'        // ISO timestamp
};

/**
 * HTTP Request task configuration schema
 */
export const HttpRequestConfigSchema = {
  url: 'string',
  method: 'string',          // GET, POST, PUT, DELETE, etc.
  headers: 'object',         // Key-value pairs
  body: 'string',            // Request body (JSON string)
  followRedirects: 'boolean',
  validateSSL: 'boolean'
};

/**
 * Webhook task configuration schema
 */
export const WebhookConfigSchema = {
  url: 'string',
  method: 'string',
  headers: 'object',
  payload: 'object',         // Webhook payload
  secret: 'string',          // Webhook secret for signature
  signatureHeader: 'string'  // Header name for signature
};

/**
 * Email task configuration schema
 */
export const EmailConfigSchema = {
  to: 'array',               // Array of email addresses
  cc: 'array',               // Array of email addresses
  bcc: 'array',              // Array of email addresses
  subject: 'string',
  body: 'string',
  isHtml: 'boolean',
  attachments: 'array'       // Array of attachment objects
};

/**
 * Validates a task object against the schema
 * @param {Object} task - Task object to validate
 * @returns {Object} - Validation result { valid: boolean, errors: array }
 */
export function validateTask(task) {
  const errors = [];

  // Required fields
  const requiredFields = ['id', 'name', 'type', 'status', 'cron'];
  for (const field of requiredFields) {
    if (!task[field]) {
      errors.push(`Missing required field: ${field}`);
    }
  }

  // Validate task type
  if (task.type && !Object.values(TaskType).includes(task.type)) {
    errors.push(`Invalid task type: ${task.type}`);
  }

  // Validate task status
  if (task.status && !Object.values(TaskStatus).includes(task.status)) {
    errors.push(`Invalid task status: ${task.status}`);
  }

  // Validate cron expression (basic validation)
  if (task.cron && !isValidCronExpression(task.cron)) {
    errors.push(`Invalid cron expression: ${task.cron}`);
  }

  // Validate retry configuration
  if (task.retryConfig) {
    if (task.retryConfig.strategy && !Object.values(RetryStrategy).includes(task.retryConfig.strategy)) {
      errors.push(`Invalid retry strategy: ${task.retryConfig.strategy}`);
    }
    if (task.retryConfig.maxRetries && (task.retryConfig.maxRetries < 0 || task.retryConfig.maxRetries > 10)) {
      errors.push('Max retries must be between 0 and 10');
    }
  }

  // Validate timeout
  if (task.timeout && (task.timeout < 1000 || task.timeout > 300000)) {
    errors.push('Timeout must be between 1000ms and 300000ms');
  }

  return {
    valid: errors.length === 0,
    errors
  };
}

/**
 * Creates a new task with default values
 * @param {Object} taskData - Partial task data
 * @returns {Object} - Complete task object
 */
export function createTask(taskData) {
  const now = new Date().toISOString();
  
  return {
    id: taskData.id || generateUUID(),
    name: taskData.name || '',
    description: taskData.description || '',
    type: taskData.type || TaskType.HTTP_REQUEST,
    status: taskData.status || TaskStatus.INACTIVE,
    cron: taskData.cron || '0 0 * * *',
    timezone: taskData.timezone || 'UTC',
    config: taskData.config || {},
    retryConfig: {
      enabled: false,
      maxRetries: 3,
      strategy: RetryStrategy.EXPONENTIAL,
      baseDelay: 1000,
      maxDelay: 60000,
      ...taskData.retryConfig
    },
    timeout: taskData.timeout || 30000,
    tags: taskData.tags || [],
    metadata: taskData.metadata || {},
    createdAt: now,
    updatedAt: now,
    lastRunAt: null,
    nextRunAt: null
  };
}

/**
 * Basic cron expression validation
 * @param {string} cron - Cron expression
 * @returns {boolean} - Whether the cron expression is valid
 */
function isValidCronExpression(cron) {
  // Basic validation - should have 5 parts separated by spaces
  const parts = cron.trim().split(/\s+/);
  return parts.length === 5;
}

/**
 * Generates a UUID v4
 * @returns {string} - UUID string
 */
function generateUUID() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}
