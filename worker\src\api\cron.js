/**
 * Cron Triggers API routes
 * Provides endpoints for managing dynamic cron triggers
 */

import { Hono } from 'hono';
import { SchedulerService } from '../services/SchedulerService.js';
import { CronUtil } from '../utils/cron.js';
import { ValidationError, NotFoundError } from '../middleware/error.js';
import { validateCronUpdateOperation } from '../utils/validation.js';

const app = new Hono();

/**
 * GET /cron/status - Get current cron trigger status and configuration
 */
app.get('/status', async (c) => {
  try {
    const schedulerService = new SchedulerService(c.env);
    const status = await schedulerService.getCronTriggerStatus();

    return c.json({
      success: true,
      data: status,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Get cron status error:', error);
    return c.json({
      error: 'Internal Server Error',
      message: 'Failed to retrieve cron status'
    }, 500);
  }
});

/**
 * GET /cron/history - Get cron trigger update history
 */
app.get('/history', async (c) => {
  try {
    const schedulerService = new SchedulerService(c.env);
    const history = await schedulerService.cloudflareAPI.getCronUpdateHistory();

    return c.json({
      success: true,
      data: history,
      count: history.length,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Get cron history error:', error);
    return c.json({
      error: 'Internal Server Error',
      message: 'Failed to retrieve cron history'
    }, 500);
  }
});

/**
 * POST /cron/update - Manually update cron triggers
 */
app.post('/update', async (c) => {
  try {
    const body = await c.req.json();
    const { triggers, reason = 'manual' } = body;

    // Validate input
    if (!Array.isArray(triggers) || triggers.length === 0) {
      throw new ValidationError('Triggers array is required and must not be empty');
    }

    if (triggers.length > 10) {
      throw new ValidationError('Maximum 10 cron triggers allowed');
    }

    // Validate each cron expression
    for (let i = 0; i < triggers.length; i++) {
      const trigger = triggers[i];

      if (typeof trigger !== 'string' || trigger.trim().length === 0) {
        throw new ValidationError(`Trigger ${i + 1} must be a non-empty string`);
      }

      const validation = CronUtil.validateCronExpression(trigger.trim());
      if (!validation.valid) {
        throw new ValidationError(`Invalid cron expression at position ${i + 1}: ${validation.error}`);
      }
    }

    // Perform the update
    const schedulerService = new SchedulerService(c.env);
    const result = await schedulerService.manualCronUpdate(triggers, reason);

    const statusCode = result.success ? 200 : (result.skipped ? 200 : 400);

    return c.json({
      success: result.success,
      data: result,
      message: result.message,
      timestamp: new Date().toISOString()
    }, statusCode);

  } catch (error) {
    if (error instanceof ValidationError) {
      throw error;
    }

    console.error('Manual cron update error:', error);
    return c.json({
      error: 'Internal Server Error',
      message: 'Failed to update cron triggers'
    }, 500);
  }
});

/**
 * POST /cron/optimize - Force cron optimization based on current tasks
 */
app.post('/optimize', async (c) => {
  try {
    const schedulerService = new SchedulerService(c.env);
    const strategy = c.env.CRON_STRATEGY || 'frequency_optimization';

    let result;
    if (strategy === 'exact_match') {
      // 使用精确匹配策略
      const { TaskService } = await import('../services/TaskService.js');
      const taskService = new TaskService(c.env);
      const activeTasks = await taskService.getActiveTasks();

      result = await schedulerService.updateTriggersWithExactMatch(new Date(), activeTasks);
    } else {
      // 使用频率优化策略
      result = await schedulerService.forceCronOptimization();
    }

    const statusCode = result.success ? 200 : 400;

    return c.json({
      success: result.success,
      data: result,
      message: result.message,
      strategy,
      timestamp: new Date().toISOString()
    }, statusCode);

  } catch (error) {
    console.error('Force cron optimization error:', error);
    return c.json({
      error: 'Internal Server Error',
      message: 'Failed to optimize cron triggers'
    }, 500);
  }
});

/**
 * GET /cron/preview - Preview optimal cron expression for current tasks
 */
app.get('/preview', async (c) => {
  try {
    const schedulerService = new SchedulerService(c.env);

    // Get current active tasks
    const { TaskService } = await import('../services/TaskService.js');
    const taskService = new TaskService(c.env);
    const activeTasks = await taskService.getActiveTasks();

    // Calculate optimal cron
    const optimalCron = CronUtil.calculateOptimalSchedulerCron(activeTasks);
    const description = CronUtil.describe(optimalCron);
    const nextRuns = CronUtil.getNextRunTimes(optimalCron, 5);

    // Get current triggers for comparison
    const currentTriggers = await schedulerService.cloudflareAPI.getCurrentCronTriggers();
    const currentMainTrigger = currentTriggers[0] || '*/5 * * * *';
    const isOptimal = currentMainTrigger === optimalCron;

    return c.json({
      success: true,
      data: {
        currentMainTrigger,
        optimalCron,
        description,
        isOptimal,
        activeTaskCount: activeTasks.length,
        nextRuns: nextRuns.map(date => date.toISOString()),
        recommendation: isOptimal ? 'Current trigger is already optimal' : `Consider updating to ${optimalCron}`,
        suggestedTriggers: [
          optimalCron,           // Main dynamic scheduler
          '*/5 * * * *',         // Backup scheduler
          '0 */1 * * *'          // Watchdog
        ]
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Cron preview error:', error);
    return c.json({
      error: 'Internal Server Error',
      message: 'Failed to generate cron preview'
    }, 500);
  }
});

/**
 * GET /cron/metrics - Get cron update metrics and statistics
 */
app.get('/metrics', async (c) => {
  try {
    const schedulerService = new SchedulerService(c.env);

    const [latestMetrics, updateHistory, systemLoad] = await Promise.all([
      schedulerService.generalKV.get('latest_cron_update_metrics'),
      schedulerService.cloudflareAPI.getCronUpdateHistory(),
      schedulerService.calculateSystemLoad()
    ]);

    // Calculate statistics from history
    const successfulUpdates = updateHistory.filter(update => update.success).length;
    const totalUpdates = updateHistory.length;
    const successRate = totalUpdates > 0 ? (successfulUpdates / totalUpdates) : 0;

    // Get recent update frequency
    const recentUpdates = updateHistory.filter(update => {
      const updateTime = new Date(update.timestamp);
      const oneDayAgo = Date.now() - (24 * 60 * 60 * 1000);
      return updateTime.getTime() > oneDayAgo;
    });

    return c.json({
      success: true,
      data: {
        latestMetrics,
        statistics: {
          totalUpdates,
          successfulUpdates,
          successRate,
          recentUpdates: recentUpdates.length,
          lastUpdateTime: updateHistory[0]?.timestamp || null
        },
        systemLoad,
        updateHistory: updateHistory.slice(0, 5) // Last 5 updates
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Get cron metrics error:', error);
    return c.json({
      error: 'Internal Server Error',
      message: 'Failed to retrieve cron metrics'
    }, 500);
  }
});

/**
 * POST /cron/validate - Validate cron expressions without updating
 */
app.post('/validate', async (c) => {
  try {
    const body = await c.req.json();
    const { triggers } = body;

    if (!Array.isArray(triggers)) {
      throw new ValidationError('Triggers must be an array');
    }

    const validationResults = triggers.map((trigger, index) => {
      if (typeof trigger !== 'string') {
        return {
          index,
          trigger,
          valid: false,
          error: 'Trigger must be a string'
        };
      }

      const validation = CronUtil.validateCronExpression(trigger.trim());
      const result = {
        index,
        trigger: trigger.trim(),
        valid: validation.valid
      };

      if (!validation.valid) {
        result.error = validation.error;
      } else {
        result.description = CronUtil.describe(trigger.trim());
        result.nextRuns = CronUtil.getNextRunTimes(trigger.trim(), 3).map(date => date.toISOString());
      }

      return result;
    });

    const allValid = validationResults.every(result => result.valid);

    return c.json({
      success: true,
      data: {
        allValid,
        results: validationResults,
        summary: {
          total: triggers.length,
          valid: validationResults.filter(r => r.valid).length,
          invalid: validationResults.filter(r => !r.valid).length
        }
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    if (error instanceof ValidationError) {
      throw error;
    }

    console.error('Cron validation error:', error);
    return c.json({
      error: 'Internal Server Error',
      message: 'Failed to validate cron expressions'
    }, 500);
  }
});

/**
 * POST /cron/validate-config - Validate system configuration for cron updates
 */
app.post('/validate-config', async (c) => {
  try {
    const schedulerService = new SchedulerService(c.env);

    // Get current system state
    const [activeTasks, systemHealth, lastUpdateTime] = await Promise.all([
      (async () => {
        try {
          const { TaskService } = await import('../services/TaskService.js');
          const taskService = new TaskService(c.env);
          return await taskService.getActiveTasks();
        } catch (error) {
          console.error('Error getting active tasks:', error);
          return [];
        }
      })(),
      schedulerService.calculateSystemLoad(),
      schedulerService.cloudflareAPI.getLastUpdateTime()
    ]);

    // Validate configuration
    const validation = validateCronUpdateOperation({
      env: c.env,
      triggers: ['*/5 * * * *'], // Default trigger for validation
      tasks: activeTasks,
      systemHealth,
      lastUpdateTime: lastUpdateTime?.getTime(),
      force: false
    });

    return c.json({
      success: true,
      data: {
        validation,
        systemState: {
          activeTaskCount: activeTasks.length,
          systemHealth,
          lastUpdateTime: lastUpdateTime?.toISOString(),
          canUpdate: validation.canProceed
        }
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Config validation error:', error);
    return c.json({
      error: 'Internal Server Error',
      message: 'Failed to validate configuration'
    }, 500);
  }
});

export default app;
