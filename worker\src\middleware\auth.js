/**
 * Authentication middleware
 */

import { JWTUtil } from '../utils/jwt.js';

/**
 * Authentication middleware
 * @param {Context} c - Hono context
 * @param {Function} next - Next middleware function
 * @returns {Promise<Response>} - Response or next middleware
 */
export async function authMiddleware(c, next) {
  try {
    // Get JWT secret from environment
    const jwtSecret = c.env.JWT_SECRET;
    if (!jwtSecret) {
      console.error('JWT_SECRET environment variable not configured');
      return c.json({
        error: 'Authentication not configured',
        message: 'JWT secret not found. Please configure JWT_SECRET environment variable.'
      }, 500);
    }

    // Get authorization header
    const authHeader = c.req.header('Authorization');
    if (!authHeader) {
      return c.json({
        error: 'Unauthorized',
        message: 'Authorization header is required'
      }, 401);
    }

    // Extract token
    const jwtUtil = new JWTUtil(jwtSecret);
    const token = jwtUtil.extractTokenFromHeader(authHeader);

    if (!token) {
      return c.json({
        error: 'Unauthorized',
        message: 'Invalid authorization header format'
      }, 401);
    }

    // Verify token
    try {
      const payload = await jwtUtil.verifyLoginToken(token);

      // Add user info to context
      c.set('user', {
        userId: payload.userId,
        ...payload
      });

      // Continue to next middleware
      await next();

    } catch (error) {
      return c.json({
        error: 'Unauthorized',
        message: 'Invalid or expired token'
      }, 401);
    }

  } catch (error) {
    console.error('Auth middleware error:', error);
    return c.json({
      error: 'Internal Server Error',
      message: 'Authentication failed'
    }, 500);
  }
}
