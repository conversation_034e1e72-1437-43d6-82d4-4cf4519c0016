/**
 * Validation utilities for cron expressions and system configuration
 */

import { CronUtil } from './cron.js';

/**
 * Validates Cloudflare API configuration
 * @param {Object} env - Environment variables
 * @returns {Object} - Validation result
 */
export function validateCloudflareAPIConfig(env) {
  const errors = [];
  const warnings = [];

  // Check required environment variables
  if (!env.CLOUDFLARE_API_TOKEN) {
    errors.push('CLOUDFLARE_API_TOKEN is required for dynamic cron updates');
  }

  if (!env.ACCOUNT_ID) {
    errors.push('ACCOUNT_ID is required for Cloudflare API calls');
  }

  if (!env.WORKER_NAME) {
    warnings.push('WORKER_NAME not set, using default: cron-task-worker');
  }

  // Validate API token format (basic check)
  if (env.CLOUDFLARE_API_TOKEN && !env.CLOUDFLARE_API_TOKEN.startsWith('Bearer ') && env.CLOUDFLARE_API_TOKEN.length < 40) {
    warnings.push('CLOUDFLARE_API_TOKEN format may be invalid');
  }

  // Validate account ID format
  if (env.ACCOUNT_ID && !/^[a-f0-9]{32}$/.test(env.ACCOUNT_ID)) {
    warnings.push('ACCOUNT_ID format may be invalid (should be 32 character hex string)');
  }

  return {
    valid: errors.length === 0,
    errors,
    warnings,
    configured: !!(env.CLOUDFLARE_API_TOKEN && env.ACCOUNT_ID)
  };
}

/**
 * Validates system configuration for dynamic cron updates
 * @param {Object} env - Environment variables
 * @returns {Object} - Validation result
 */
export function validateSystemConfig(env) {
  const errors = [];
  const warnings = [];

  // Check cron update interval
  const minInterval = parseInt(env.CRON_UPDATE_MIN_INTERVAL);
  if (isNaN(minInterval) || minInterval < 60000) {
    warnings.push('CRON_UPDATE_MIN_INTERVAL should be at least 60000ms (1 minute)');
  }

  // Check if dynamic cron is enabled
  const dynamicEnabled = env.ENABLE_DYNAMIC_CRON === 'true';
  if (!dynamicEnabled) {
    warnings.push('Dynamic cron updates are disabled (ENABLE_DYNAMIC_CRON=false)');
  }

  // Check worker name
  const workerName = env.WORKER_NAME;
  if (workerName && !/^[a-z0-9-]+$/.test(workerName)) {
    warnings.push('WORKER_NAME should only contain lowercase letters, numbers, and hyphens');
  }

  return {
    valid: errors.length === 0,
    errors,
    warnings,
    dynamicCronEnabled: dynamicEnabled,
    minUpdateInterval: minInterval || 300000
  };
}

/**
 * Validates cron trigger array for Cloudflare Workers
 * @param {Array} triggers - Array of cron expressions
 * @returns {Object} - Validation result
 */
export function validateCronTriggers(triggers) {
  const errors = [];
  const warnings = [];

  if (!Array.isArray(triggers)) {
    errors.push('Triggers must be an array');
    return { valid: false, errors, warnings };
  }

  if (triggers.length === 0) {
    errors.push('At least one cron trigger is required');
    return { valid: false, errors, warnings };
  }

  if (triggers.length > 10) {
    errors.push('Maximum 10 cron triggers allowed by Cloudflare Workers');
    return { valid: false, errors, warnings };
  }

  // Validate each trigger
  triggers.forEach((trigger, index) => {
    if (typeof trigger !== 'string' || trigger.trim().length === 0) {
      errors.push(`Trigger ${index + 1} must be a non-empty string`);
      return;
    }

    const cronValidation = CronUtil.validateCronExpression(trigger.trim());
    if (!cronValidation.valid) {
      errors.push(`Trigger ${index + 1} is invalid: ${cronValidation.error}`);
      return;
    }

    // Check for very frequent triggers (potential performance issue)
    if (CronUtil.isFrequentSchedule(trigger.trim())) {
      const description = CronUtil.describe(trigger.trim());
      if (trigger.includes('*/1 ') && !trigger.startsWith('*/1 ')) {
        warnings.push(`Trigger ${index + 1} (${description}) is very frequent and may impact performance`);
      }
    }
  });

  // Check for duplicate triggers
  const uniqueTriggers = new Set(triggers.map(t => t.trim()));
  if (uniqueTriggers.size !== triggers.length) {
    warnings.push('Duplicate cron triggers detected');
  }

  return {
    valid: errors.length === 0,
    errors,
    warnings,
    triggerCount: triggers.length,
    hasFrequentTriggers: triggers.some(t => CronUtil.isFrequentSchedule(t.trim()))
  };
}

/**
 * Validates task configuration for optimal scheduling
 * @param {Array} tasks - Array of task objects
 * @returns {Object} - Validation result
 */
export function validateTasksForScheduling(tasks) {
  const errors = [];
  const warnings = [];
  const analysis = {
    totalTasks: tasks.length,
    activeTasks: 0,
    invalidCronTasks: 0,
    frequentTasks: 0,
    tasksByFrequency: {
      minutely: 0,
      hourly: 0,
      daily: 0,
      weekly: 0,
      custom: 0
    }
  };

  if (!Array.isArray(tasks)) {
    errors.push('Tasks must be an array');
    return { valid: false, errors, warnings, analysis };
  }

  tasks.forEach((task, index) => {
    if (!task || typeof task !== 'object') {
      errors.push(`Task ${index + 1} must be an object`);
      return;
    }

    if (task.status === 'active') {
      analysis.activeTasks++;

      if (!task.cron) {
        warnings.push(`Active task ${task.name || index + 1} has no cron expression`);
        return;
      }

      const cronValidation = CronUtil.validateCronExpression(task.cron);
      if (!cronValidation.valid) {
        analysis.invalidCronTasks++;
        warnings.push(`Task ${task.name || index + 1} has invalid cron: ${cronValidation.error}`);
        return;
      }

      // Categorize task frequency
      const parts = task.cron.split(' ');
      const [minute, hour] = parts;

      if (minute.includes('*') && hour === '*') {
        analysis.tasksByFrequency.minutely++;
        if (CronUtil.isFrequentSchedule(task.cron)) {
          analysis.frequentTasks++;
        }
      } else if (hour.includes('*')) {
        analysis.tasksByFrequency.hourly++;
      } else if (parts[2] === '*' && parts[3] === '*' && parts[4] === '*') {
        analysis.tasksByFrequency.daily++;
      } else if (parts[4] !== '*') {
        analysis.tasksByFrequency.weekly++;
      } else {
        analysis.tasksByFrequency.custom++;
      }
    }
  });

  // Generate warnings based on analysis
  if (analysis.activeTasks === 0) {
    warnings.push('No active tasks found - scheduler optimization may not be effective');
  }

  if (analysis.frequentTasks > 10) {
    warnings.push(`${analysis.frequentTasks} tasks run very frequently - consider optimizing schedules`);
  }

  if (analysis.invalidCronTasks > 0) {
    warnings.push(`${analysis.invalidCronTasks} tasks have invalid cron expressions`);
  }

  return {
    valid: errors.length === 0,
    errors,
    warnings,
    analysis
  };
}

/**
 * Validates rate limiting configuration
 * @param {number} lastUpdateTime - Timestamp of last update
 * @param {number} minInterval - Minimum interval between updates
 * @returns {Object} - Validation result
 */
export function validateRateLimit(lastUpdateTime, minInterval = 300000) {
  const now = Date.now();
  const timeSinceLastUpdate = lastUpdateTime ? now - lastUpdateTime : Infinity;
  const canUpdate = timeSinceLastUpdate >= minInterval;

  return {
    canUpdate,
    timeSinceLastUpdate,
    timeUntilNextUpdate: canUpdate ? 0 : minInterval - timeSinceLastUpdate,
    nextUpdateTime: canUpdate ? now : lastUpdateTime + minInterval
  };
}

/**
 * Validates system health for cron updates
 * @param {Object} systemHealth - System health data
 * @returns {Object} - Validation result
 */
export function validateSystemHealth(systemHealth) {
  const errors = [];
  const warnings = [];
  let healthScore = 100;

  if (!systemHealth || typeof systemHealth !== 'object') {
    errors.push('System health data is required');
    return { valid: false, errors, warnings, healthScore: 0 };
  }

  // Check KV storage health
  if (systemHealth.checks?.kv_storage !== 'healthy') {
    errors.push('KV storage is not healthy');
    healthScore -= 30;
  }

  // Check Durable Objects health
  if (systemHealth.checks?.durable_objects !== 'healthy') {
    warnings.push('Durable Objects health check failed');
    healthScore -= 20;
  }

  // Check failure rate
  if (systemHealth.failureRate > 0.3) {
    warnings.push('High failure rate detected (>30%)');
    healthScore -= 25;
  } else if (systemHealth.failureRate > 0.1) {
    warnings.push('Elevated failure rate detected (>10%)');
    healthScore -= 10;
  }

  // Check system load
  if (systemHealth.systemLoad > 0.8) {
    warnings.push('High system load detected');
    healthScore -= 15;
  }

  return {
    valid: errors.length === 0,
    errors,
    warnings,
    healthScore: Math.max(0, healthScore),
    recommendUpdate: healthScore >= 70
  };
}

/**
 * Comprehensive validation for cron update operation
 * @param {Object} params - Validation parameters
 * @returns {Object} - Comprehensive validation result
 */
export function validateCronUpdateOperation(params) {
  const {
    env,
    triggers,
    tasks,
    systemHealth,
    lastUpdateTime,
    force = false
  } = params;

  const results = {
    apiConfig: validateCloudflareAPIConfig(env),
    systemConfig: validateSystemConfig(env),
    triggers: validateCronTriggers(triggers),
    tasks: validateTasksForScheduling(tasks || []),
    rateLimit: validateRateLimit(lastUpdateTime, env.CRON_UPDATE_MIN_INTERVAL),
    systemHealth: validateSystemHealth(systemHealth)
  };

  const allErrors = Object.values(results).flatMap(r => r.errors || []);
  const allWarnings = Object.values(results).flatMap(r => r.warnings || []);

  const canProceed = force || (
    allErrors.length === 0 &&
    results.apiConfig.configured &&
    results.systemConfig.dynamicCronEnabled &&
    (force || results.rateLimit.canUpdate) &&
    results.systemHealth.recommendUpdate
  );

  return {
    valid: allErrors.length === 0,
    canProceed,
    errors: allErrors,
    warnings: allWarnings,
    results,
    summary: {
      totalErrors: allErrors.length,
      totalWarnings: allWarnings.length,
      apiConfigured: results.apiConfig.configured,
      dynamicEnabled: results.systemConfig.dynamicCronEnabled,
      rateLimited: !results.rateLimit.canUpdate,
      systemHealthy: results.systemHealth.recommendUpdate
    }
  };
}
