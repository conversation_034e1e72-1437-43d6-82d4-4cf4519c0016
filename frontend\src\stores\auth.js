import { defineStore } from 'pinia'
import { authApi } from '@/api/auth'
import { ElMessage } from 'element-plus'

export const useAuthStore = defineStore('auth', {
  state: () => ({
    isAuthenticated: false,
    user: null,
    token: null,
    loading: false,
    error: null
  }),

  getters: {
    isLoggedIn: (state) => state.isAuthenticated && !!state.token,
    userInfo: (state) => state.user,
    authToken: (state) => state.token
  },

  actions: {
    /**
     * Initialize auth state from localStorage
     */
    async initializeAuth() {
      const token = localStorage.getItem('auth_token')
      const userInfo = localStorage.getItem('user_info')

      if (token && userInfo) {
        try {
          this.token = token
          this.user = JSON.parse(userInfo)
          
          // Verify token with server
          await this.verifyToken()
        } catch (error) {
          console.error('Auth initialization failed:', error)
          this.clearAuth()
        }
      }
    },

    /**
     * Login with password
     * @param {string} password - User password
     */
    async login(password) {
      this.loading = true
      this.error = null

      try {
        const response = await authApi.login(password)
        
        if (response.success) {
          this.token = response.token
          this.user = response.user
          this.isAuthenticated = true

          // Store in localStorage
          localStorage.setItem('auth_token', response.token)
          localStorage.setItem('user_info', JSON.stringify(response.user))

          ElMessage.success('Login successful')
          return { success: true }
        } else {
          throw new Error(response.message || 'Login failed')
        }
      } catch (error) {
        this.error = error.response?.data?.message || error.message || 'Login failed'
        ElMessage.error(this.error)
        return { success: false, error: this.error }
      } finally {
        this.loading = false
      }
    },

    /**
     * Verify current token
     */
    async verifyToken() {
      try {
        const response = await authApi.verify()
        
        if (response.valid) {
          this.isAuthenticated = true
          this.user = response.user
          return true
        } else {
          this.clearAuth()
          return false
        }
      } catch (error) {
        console.error('Token verification failed:', error)
        this.clearAuth()
        return false
      }
    },

    /**
     * Logout user
     */
    async logout() {
      this.loading = true

      try {
        await authApi.logout()
      } catch (error) {
        console.error('Logout API call failed:', error)
        // Continue with local logout even if API call fails
      } finally {
        this.clearAuth()
        this.loading = false
        ElMessage.success('Logged out successfully')
      }
    },

    /**
     * Clear authentication state
     */
    clearAuth() {
      this.isAuthenticated = false
      this.user = null
      this.token = null
      this.error = null

      // Clear localStorage
      localStorage.removeItem('auth_token')
      localStorage.removeItem('user_info')
    },

    /**
     * Update user information
     * @param {Object} userInfo - Updated user info
     */
    updateUser(userInfo) {
      this.user = { ...this.user, ...userInfo }
      localStorage.setItem('user_info', JSON.stringify(this.user))
    },

    /**
     * Check if token is expired
     */
    isTokenExpired() {
      if (!this.token) return true

      try {
        // Decode JWT token to check expiration
        const payload = JSON.parse(atob(this.token.split('.')[1]))
        const currentTime = Date.now() / 1000
        
        return payload.exp < currentTime
      } catch (error) {
        console.error('Error checking token expiration:', error)
        return true
      }
    },

    /**
     * Refresh token if needed
     */
    async refreshTokenIfNeeded() {
      if (this.isTokenExpired()) {
        await this.verifyToken()
      }
    }
  }
})
