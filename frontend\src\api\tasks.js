import apiClient from './client'

export const tasksApi = {
  /**
   * Get all tasks
   * @returns {Promise} - Tasks response
   */
  async getTasks() {
    const response = await apiClient.get('/tasks')
    return response.data
  },

  /**
   * Get task by ID
   * @param {string} id - Task ID
   * @returns {Promise} - Task response
   */
  async getTask(id) {
    const response = await apiClient.get(`/tasks/${id}`)
    return response.data
  },

  /**
   * Create new task
   * @param {Object} taskData - Task data
   * @returns {Promise} - Create response
   */
  async createTask(taskData) {
    const response = await apiClient.post('/tasks', taskData)
    return response.data
  },

  /**
   * Update task
   * @param {string} id - Task ID
   * @param {Object} taskData - Updated task data
   * @returns {Promise} - Update response
   */
  async updateTask(id, taskData) {
    const response = await apiClient.put(`/tasks/${id}`, taskData)
    return response.data
  },

  /**
   * Delete task
   * @param {string} id - Task ID
   * @returns {Promise} - Delete response
   */
  async deleteTask(id) {
    const response = await apiClient.delete(`/tasks/${id}`)
    return response.data
  },

  /**
   * Manually run task
   * @param {string} id - Task ID
   * @returns {Promise} - Run response
   */
  async runTask(id) {
    const response = await apiClient.post(`/tasks/${id}/run`)
    return response.data
  },

  /**
   * Get available task types
   * @returns {Promise} - Task types response
   */
  async getTaskTypes() {
    const response = await apiClient.get('/tasks/types')
    return response.data
  }
}
