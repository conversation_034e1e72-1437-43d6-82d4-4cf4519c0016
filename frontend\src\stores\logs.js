import { defineStore } from 'pinia'
import { logsApi } from '@/api/logs'
import { ElMessage } from 'element-plus'

export const useLogsStore = defineStore('logs', {
  state: () => ({
    logs: [],
    loading: false,
    error: null,
    filters: {
      type: '',
      level: '',
      taskId: '',
      limit: 50
    },
    stats: {
      total: 0,
      byStatus: {},
      byLevel: {},
      byType: {},
      byTimeRange: {},
      topFailingTasks: [],
      averageExecutionTime: 0
    }
  }),

  getters: {
    filteredLogs: (state) => {
      let filtered = [...state.logs]

      // Apply type filter
      if (state.filters.type) {
        if (state.filters.type === 'failure') {
          filtered = filtered.filter(log => 
            log.status === 'failure' || log.status === 'timeout'
          )
        } else if (state.filters.type === 'success') {
          filtered = filtered.filter(log => log.status === 'success')
        }
      }

      // Apply level filter
      if (state.filters.level) {
        filtered = filtered.filter(log => log.level === state.filters.level)
      }

      // Apply task filter
      if (state.filters.taskId) {
        filtered = filtered.filter(log => log.taskId === state.filters.taskId)
      }

      return filtered
    },

    failureLogs: (state) => state.logs.filter(log => 
      log.status === 'failure' || log.status === 'timeout'
    ),

    successLogs: (state) => state.logs.filter(log => log.status === 'success'),

    recentLogs: (state) => {
      const oneHourAgo = Date.now() - 60 * 60 * 1000
      return state.logs.filter(log => 
        new Date(log.timestamp).getTime() > oneHourAgo
      )
    },

    logsByLevel: (state) => {
      const byLevel = {}
      state.logs.forEach(log => {
        byLevel[log.level] = (byLevel[log.level] || 0) + 1
      })
      return byLevel
    },

    logsByStatus: (state) => {
      const byStatus = {}
      state.logs.forEach(log => {
        byStatus[log.status] = (byStatus[log.status] || 0) + 1
      })
      return byStatus
    }
  },

  actions: {
    /**
     * Fetch logs with current filters
     */
    async fetchLogs() {
      this.loading = true
      this.error = null

      try {
        const response = await logsApi.getLogs(this.filters)
        
        if (response.success) {
          this.logs = response.data
        } else {
          throw new Error(response.message || 'Failed to fetch logs')
        }
      } catch (error) {
        this.error = error.response?.data?.message || error.message || 'Failed to fetch logs'
        ElMessage.error(this.error)
      } finally {
        this.loading = false
      }
    },

    /**
     * Fetch log statistics
     */
    async fetchLogStats() {
      try {
        const response = await logsApi.getLogStats()
        
        if (response.success) {
          this.stats = response.data
        }
      } catch (error) {
        console.error('Failed to fetch log stats:', error)
      }
    },

    /**
     * Get specific log by ID
     * @param {string} id - Log ID
     */
    async fetchLog(id) {
      this.loading = true
      this.error = null

      try {
        const response = await logsApi.getLog(id)
        
        if (response.success) {
          return response.data
        } else {
          throw new Error(response.message || 'Failed to fetch log')
        }
      } catch (error) {
        this.error = error.response?.data?.message || error.message || 'Failed to fetch log'
        ElMessage.error(this.error)
        return null
      } finally {
        this.loading = false
      }
    },

    /**
     * Export logs
     * @param {Object} options - Export options
     */
    async exportLogs(options = {}) {
      try {
        const response = await logsApi.exportLogs(options)
        return response
      } catch (error) {
        const errorMsg = error.response?.data?.message || error.message || 'Failed to export logs'
        ElMessage.error(errorMsg)
        throw error
      }
    },

    /**
     * Clear all logs
     */
    async clearLogs() {
      this.loading = true

      try {
        const response = await logsApi.clearLogs()
        
        if (response.success) {
          this.logs = []
          this.stats = {
            total: 0,
            byStatus: {},
            byLevel: {},
            byType: {},
            byTimeRange: {},
            topFailingTasks: [],
            averageExecutionTime: 0
          }
          ElMessage.success('All logs cleared successfully')
        } else {
          throw new Error(response.message || 'Failed to clear logs')
        }
      } catch (error) {
        this.error = error.response?.data?.message || error.message || 'Failed to clear logs'
        ElMessage.error(this.error)
        throw error
      } finally {
        this.loading = false
      }
    },

    /**
     * Update filters
     * @param {Object} newFilters - New filter values
     */
    updateFilters(newFilters) {
      this.filters = { ...this.filters, ...newFilters }
    },

    /**
     * Clear filters
     */
    clearFilters() {
      this.filters = {
        type: '',
        level: '',
        taskId: '',
        limit: 50
      }
    },

    /**
     * Get logs for specific task
     * @param {string} taskId - Task ID
     * @param {number} limit - Number of logs to fetch
     */
    async getTaskLogs(taskId, limit = 20) {
      try {
        const response = await logsApi.getLogs({ taskId, limit })
        
        if (response.success) {
          return response.data
        } else {
          throw new Error(response.message || 'Failed to fetch task logs')
        }
      } catch (error) {
        console.error('Failed to fetch task logs:', error)
        return []
      }
    },

    /**
     * Get failure logs
     * @param {number} limit - Number of logs to fetch
     */
    async getFailureLogs(limit = 20) {
      try {
        const response = await logsApi.getLogs({ type: 'failure', limit })
        
        if (response.success) {
          return response.data
        } else {
          throw new Error(response.message || 'Failed to fetch failure logs')
        }
      } catch (error) {
        console.error('Failed to fetch failure logs:', error)
        return []
      }
    },

    /**
     * Add a new log entry (for real-time updates)
     * @param {Object} log - Log entry
     */
    addLog(log) {
      this.logs.unshift(log)
      
      // Keep only the latest logs based on limit
      if (this.logs.length > this.filters.limit) {
        this.logs = this.logs.slice(0, this.filters.limit)
      }
    },

    /**
     * Remove old logs (cleanup)
     * @param {number} maxAge - Maximum age in milliseconds
     */
    removeOldLogs(maxAge = 7 * 24 * 60 * 60 * 1000) { // 7 days default
      const cutoffTime = Date.now() - maxAge
      this.logs = this.logs.filter(log => 
        new Date(log.timestamp).getTime() > cutoffTime
      )
    }
  }
})
