/**
 * Log data schema and utilities
 */

/**
 * Log level enumeration
 */
export const LogLevel = {
  INFO: 'info',
  WARN: 'warn',
  ERROR: 'error',
  DEBUG: 'debug'
};

/**
 * Log type enumeration
 */
export const LogType = {
  TASK_EXECUTION: 'task_execution',
  TASK_FAILURE: 'task_failure',
  SYSTEM: 'system',
  SCHEDULER: 'scheduler'
};

/**
 * Execution status enumeration
 */
export const ExecutionStatus = {
  SUCCESS: 'success',
  FAILURE: 'failure',
  TIMEOUT: 'timeout',
  RETRY: 'retry'
};

/**
 * Log entry schema
 */
export const LogSchema = {
  id: 'string',              // UUID
  taskId: 'string',          // Task UUID
  taskName: 'string',        // Task name for easier identification
  type: 'string',            // LogType
  level: 'string',           // LogLevel
  status: 'string',          // ExecutionStatus
  message: 'string',         // Log message
  details: 'object',         // Additional details
  executionTime: 'number',   // Execution time in milliseconds
  retryCount: 'number',      // Current retry count
  timestamp: 'string',       // ISO timestamp
  metadata: 'object'         // Additional metadata
};

/**
 * Task execution log details schema
 */
export const ExecutionDetailsSchema = {
  request: {
    url: 'string',
    method: 'string',
    headers: 'object',
    body: 'string'
  },
  response: {
    status: 'number',
    statusText: 'string',
    headers: 'object',
    body: 'string'
  },
  error: {
    name: 'string',
    message: 'string',
    stack: 'string'
  },
  timing: {
    startTime: 'string',
    endTime: 'string',
    duration: 'number'
  }
};

/**
 * Creates a new log entry
 * @param {Object} logData - Log data
 * @returns {Object} - Complete log entry
 */
export function createLogEntry(logData) {
  return {
    id: logData.id || generateUUID(),
    taskId: logData.taskId || '',
    taskName: logData.taskName || '',
    type: logData.type || LogType.TASK_EXECUTION,
    level: logData.level || LogLevel.INFO,
    status: logData.status || ExecutionStatus.SUCCESS,
    message: logData.message || '',
    details: logData.details || {},
    executionTime: logData.executionTime || 0,
    retryCount: logData.retryCount || 0,
    timestamp: logData.timestamp || new Date().toISOString(),
    metadata: logData.metadata || {}
  };
}

/**
 * Creates a task execution success log
 * @param {string} taskId - Task ID
 * @param {string} taskName - Task name
 * @param {Object} details - Execution details
 * @param {number} executionTime - Execution time in ms
 * @returns {Object} - Log entry
 */
export function createSuccessLog(taskId, taskName, details, executionTime) {
  return createLogEntry({
    taskId,
    taskName,
    type: LogType.TASK_EXECUTION,
    level: LogLevel.INFO,
    status: ExecutionStatus.SUCCESS,
    message: `Task executed successfully`,
    details,
    executionTime
  });
}

/**
 * Creates a task execution failure log
 * @param {string} taskId - Task ID
 * @param {string} taskName - Task name
 * @param {Error} error - Error object
 * @param {Object} details - Execution details
 * @param {number} retryCount - Current retry count
 * @param {number} executionTime - Execution time in ms
 * @returns {Object} - Log entry
 */
export function createFailureLog(taskId, taskName, error, details, retryCount = 0, executionTime = 0) {
  return createLogEntry({
    taskId,
    taskName,
    type: LogType.TASK_FAILURE,
    level: LogLevel.ERROR,
    status: ExecutionStatus.FAILURE,
    message: `Task execution failed: ${error.message}`,
    details: {
      ...details,
      error: {
        name: error.name,
        message: error.message,
        stack: error.stack
      }
    },
    executionTime,
    retryCount
  });
}

/**
 * Creates a task timeout log
 * @param {string} taskId - Task ID
 * @param {string} taskName - Task name
 * @param {number} timeout - Timeout value in ms
 * @param {number} retryCount - Current retry count
 * @returns {Object} - Log entry
 */
export function createTimeoutLog(taskId, taskName, timeout, retryCount = 0) {
  return createLogEntry({
    taskId,
    taskName,
    type: LogType.TASK_FAILURE,
    level: LogLevel.WARN,
    status: ExecutionStatus.TIMEOUT,
    message: `Task execution timed out after ${timeout}ms`,
    details: { timeout },
    executionTime: timeout,
    retryCount
  });
}

/**
 * Creates a retry log
 * @param {string} taskId - Task ID
 * @param {string} taskName - Task name
 * @param {number} retryCount - Current retry count
 * @param {number} delay - Retry delay in ms
 * @param {string} reason - Retry reason
 * @returns {Object} - Log entry
 */
export function createRetryLog(taskId, taskName, retryCount, delay, reason) {
  return createLogEntry({
    taskId,
    taskName,
    type: LogType.TASK_EXECUTION,
    level: LogLevel.WARN,
    status: ExecutionStatus.RETRY,
    message: `Task will retry in ${delay}ms (attempt ${retryCount})`,
    details: { delay, reason },
    retryCount
  });
}

/**
 * Creates a system log
 * @param {string} message - Log message
 * @param {string} level - Log level
 * @param {Object} details - Additional details
 * @returns {Object} - Log entry
 */
export function createSystemLog(message, level = LogLevel.INFO, details = {}) {
  return createLogEntry({
    type: LogType.SYSTEM,
    level,
    message,
    details
  });
}

/**
 * Validates a log entry
 * @param {Object} log - Log entry to validate
 * @returns {Object} - Validation result { valid: boolean, errors: array }
 */
export function validateLog(log) {
  const errors = [];

  // Required fields
  const requiredFields = ['type', 'level', 'message', 'timestamp'];
  for (const field of requiredFields) {
    if (!log[field]) {
      errors.push(`Missing required field: ${field}`);
    }
  }

  // Validate log type
  if (log.type && !Object.values(LogType).includes(log.type)) {
    errors.push(`Invalid log type: ${log.type}`);
  }

  // Validate log level
  if (log.level && !Object.values(LogLevel).includes(log.level)) {
    errors.push(`Invalid log level: ${log.level}`);
  }

  // Validate execution status
  if (log.status && !Object.values(ExecutionStatus).includes(log.status)) {
    errors.push(`Invalid execution status: ${log.status}`);
  }

  return {
    valid: errors.length === 0,
    errors
  };
}

/**
 * Generates a UUID v4
 * @returns {string} - UUID string
 */
function generateUUID() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}
