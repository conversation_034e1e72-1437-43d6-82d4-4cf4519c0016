/**
 * Cloudflare API Service - Handles integration with Cloudflare Workers API
 * Provides functionality to dynamically update cron triggers and manage worker configuration
 */

import { createSystemLog } from '../schemas/log.js';
import { validateCloudflareAPIConfig, validateCronTriggers } from '../utils/validation.js';

export class CloudflareAPIService {
  constructor(env) {
    this.env = env;
    this.apiToken = env.CLOUDFLARE_API_TOKEN;
    this.accountId = env.ACCOUNT_ID;
    this.workerName = env.WORKER_NAME || 'cron-task-worker';
    this.baseUrl = 'https://api.cloudflare.com/client/v4';

    // Configuration
    this.retryAttempts = 3;
    this.retryDelay = 1000; // 1 second
    this.requestTimeout = 30000; // 30 seconds

    // Validate configuration on initialization
    this.configValidation = validateCloudflareAPIConfig(env);
    if (this.configValidation.warnings.length > 0) {
      console.warn('Cloudflare API configuration warnings:', this.configValidation.warnings);
    }
    if (this.configValidation.errors.length > 0) {
      console.error('Cloudflare API configuration errors:', this.configValidation.errors);
    }
  }

  /**
   * Checks if the service is properly configured
   * @returns {boolean} - Whether the service is configured
   */
  isConfigured() {
    return !!(this.apiToken && this.accountId && this.workerName);
  }

  /**
   * Gets the current worker configuration including cron triggers
   * @returns {Promise<Object>} - Worker configuration
   */
  async getWorkerConfig() {
    if (!this.isConfigured()) {
      throw new Error('Cloudflare API service is not properly configured');
    }

    try {
      const url = `${this.baseUrl}/accounts/${this.accountId}/workers/scripts/${this.workerName}`;
      const response = await this.makeRequest('GET', url);

      if (!response.success) {
        throw new Error(`Failed to get worker config: ${response.errors?.[0]?.message || 'Unknown error'}`);
      }

      return response.result;
    } catch (error) {
      console.error('Error getting worker config:', error);
      throw error;
    }
  }

  /**
   * Gets the current cron triggers for the worker
   * @returns {Promise<Array>} - Array of cron trigger configurations
   */
  async getCurrentCronTriggers() {
    try {
      const config = await this.getWorkerConfig();
      return config.cron_triggers || [];
    } catch (error) {
      console.error('Error getting current cron triggers:', error);
      return [];
    }
  }

  /**
   * Updates cron triggers for the worker
   * @param {Array} cronTriggers - Array of cron expressions
   * @returns {Promise<boolean>} - Success status
   */
  async updateCronTriggers(cronTriggers) {
    if (!this.isConfigured()) {
      console.warn('Cloudflare API service is not configured, skipping cron update');
      return false;
    }

    // Validate cron triggers
    const validation = validateCronTriggers(cronTriggers);
    if (!validation.valid) {
      throw new Error(`Invalid cron triggers: ${validation.errors.join(', ')}`);
    }

    if (validation.warnings.length > 0) {
      console.warn('Cron trigger warnings:', validation.warnings);
    }

    try {
      const url = `${this.baseUrl}/accounts/${this.accountId}/workers/scripts/${this.workerName}/schedules`;

      // Format cron triggers for API - Cloudflare expects an array of objects with only 'cron' property
      const formattedTriggers = cronTriggers.map(cron => ({
        cron: cron
      }));

      // Send the array directly, not wrapped in a 'schedules' object
      const response = await this.makeRequest('PUT', url, formattedTriggers);

      if (!response.success) {
        throw new Error(`Failed to update cron triggers: ${response.errors?.[0]?.message || 'Unknown error'}`);
      }

      console.log('Successfully updated cron triggers:', cronTriggers);
      return true;
    } catch (error) {
      console.error('Error updating cron triggers:', error);
      throw error;
    }
  }

  /**
   * Updates a single cron trigger (replaces the first dynamic trigger)
   * @param {string} newCronExpression - New cron expression
   * @param {number} triggerIndex - Index of trigger to update (default: 0)
   * @returns {Promise<boolean>} - Success status
   */
  async updateSingleCronTrigger(newCronExpression, triggerIndex = 0) {
    try {
      const currentTriggers = await this.getCurrentCronTriggers();

      if (currentTriggers.length === 0) {
        // No existing triggers, create new ones with default backup triggers
        const defaultTriggers = [
          newCronExpression,
          '*/5 * * * *', // Backup scheduler
          '0 */1 * * *'  // Watchdog
        ];
        return await this.updateCronTriggers(defaultTriggers);
      }

      // Update the specified trigger
      const updatedTriggers = [...currentTriggers];
      if (triggerIndex < updatedTriggers.length) {
        updatedTriggers[triggerIndex] = newCronExpression;
      } else {
        // Add new trigger if index is beyond current array
        updatedTriggers.push(newCronExpression);
      }

      return await this.updateCronTriggers(updatedTriggers);
    } catch (error) {
      console.error('Error updating single cron trigger:', error);
      throw error;
    }
  }

  /**
   * Makes an authenticated request to Cloudflare API with retry logic
   * @param {string} method - HTTP method
   * @param {string} url - Request URL
   * @param {Object} data - Request body data
   * @returns {Promise<Object>} - API response
   */
  async makeRequest(method, url, data = null) {
    const headers = {
      'Authorization': `Bearer ${this.apiToken}`,
      'Content-Type': 'application/json',
      'User-Agent': 'cron-task-worker/1.0.0'
    };

    const requestOptions = {
      method,
      headers,
      signal: AbortSignal.timeout(this.requestTimeout)
    };

    if (data && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
      const jsonBody = JSON.stringify(data);
      requestOptions.body = jsonBody;

      // Log request body for debugging (only in development)
      if (this.env.NODE_ENV === 'development') {
        console.log(`Request body: ${jsonBody}`);
      }
    }

    let lastError;

    for (let attempt = 1; attempt <= this.retryAttempts; attempt++) {
      try {
        console.log(`Making Cloudflare API request (attempt ${attempt}/${this.retryAttempts}): ${method} ${url}`);

        const response = await fetch(url, requestOptions);

        // Try to parse response as JSON, but handle cases where it might not be JSON
        let responseData;
        try {
          responseData = await response.json();
        } catch (parseError) {
          const responseText = await response.text();
          console.error('Failed to parse response as JSON:', parseError);
          console.error('Response text:', responseText);
          throw new Error(`HTTP ${response.status}: Invalid JSON response - ${responseText}`);
        }

        if (!response.ok) {
          const errorMessage = responseData.errors?.[0]?.message || response.statusText;
          console.error('API Error Response:', responseData);
          throw new Error(`HTTP ${response.status}: ${errorMessage}`);
        }

        return responseData;
      } catch (error) {
        lastError = error;
        console.error(`Cloudflare API request failed (attempt ${attempt}/${this.retryAttempts}):`, error);

        if (attempt < this.retryAttempts) {
          const delay = this.retryDelay * Math.pow(2, attempt - 1); // Exponential backoff
          console.log(`Retrying in ${delay}ms...`);
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }

    throw lastError;
  }

  /**
   * Validates cron trigger configuration before updating
   * @param {Array} cronTriggers - Array of cron expressions to validate
   * @returns {Object} - Validation result
   */
  validateCronTriggers(cronTriggers) {
    const errors = [];

    if (!Array.isArray(cronTriggers)) {
      errors.push('Cron triggers must be an array');
      return { valid: false, errors };
    }

    if (cronTriggers.length === 0) {
      errors.push('At least one cron trigger is required');
      return { valid: false, errors };
    }

    if (cronTriggers.length > 10) {
      errors.push('Maximum 10 cron triggers allowed');
      return { valid: false, errors };
    }

    // Validate each cron expression
    for (let i = 0; i < cronTriggers.length; i++) {
      const cron = cronTriggers[i];

      if (typeof cron !== 'string' || cron.trim().length === 0) {
        errors.push(`Cron trigger ${i + 1} must be a non-empty string`);
        continue;
      }

      // Basic cron format validation (5 fields)
      const parts = cron.trim().split(/\s+/);
      if (parts.length !== 5) {
        errors.push(`Cron trigger ${i + 1} must have exactly 5 fields (minute hour day month weekday)`);
      }
    }

    return { valid: errors.length === 0, errors };
  }

  /**
   * Gets cron trigger update history from KV storage
   * @returns {Promise<Array>} - Array of update history records
   */
  async getCronUpdateHistory() {
    try {
      const historyKey = 'cron_update_history';
      const history = await this.env.TASKS_KV.get(historyKey, { type: 'json' });
      return history || [];
    } catch (error) {
      console.error('Error getting cron update history:', error);
      return [];
    }
  }

  /**
   * Records a cron trigger update in history
   * @param {Array} oldTriggers - Previous cron triggers
   * @param {Array} newTriggers - New cron triggers
   * @param {boolean} success - Whether the update was successful
   * @param {string} reason - Reason for the update
   * @returns {Promise<void>}
   */
  async recordCronUpdate(oldTriggers, newTriggers, success, reason = 'automatic') {
    try {
      const history = await this.getCronUpdateHistory();

      const updateRecord = {
        timestamp: new Date().toISOString(),
        oldTriggers: oldTriggers || [],
        newTriggers: newTriggers || [],
        success,
        reason,
        id: crypto.randomUUID()
      };

      // Keep only last 50 records
      history.unshift(updateRecord);
      if (history.length > 50) {
        history.splice(50);
      }

      const historyKey = 'cron_update_history';
      await this.env.TASKS_KV.put(historyKey, JSON.stringify(history));

      console.log('Recorded cron update in history:', updateRecord);
    } catch (error) {
      console.error('Error recording cron update history:', error);
    }
  }

  /**
   * Gets the last successful cron update time
   * @returns {Promise<Date|null>} - Last update time or null
   */
  async getLastUpdateTime() {
    try {
      const history = await this.getCronUpdateHistory();
      const lastSuccessful = history.find(record => record.success);
      return lastSuccessful ? new Date(lastSuccessful.timestamp) : null;
    } catch (error) {
      console.error('Error getting last update time:', error);
      return null;
    }
  }

  /**
   * Checks if enough time has passed since last update (rate limiting)
   * @returns {Promise<boolean>} - Whether update is allowed
   */
  async canUpdateNow() {
    try {
      const lastUpdate = await this.getLastUpdateTime();
      if (!lastUpdate) {
        return true; // No previous update, allow
      }

      const minInterval = parseInt(this.env.CRON_UPDATE_MIN_INTERVAL) || 300000; // 5 minutes default
      const timeSinceLastUpdate = Date.now() - lastUpdate.getTime();

      return timeSinceLastUpdate >= minInterval;
    } catch (error) {
      console.error('Error checking update rate limit:', error);
      return false;
    }
  }

  /**
   * Performs a safe cron trigger update with validation and rate limiting
   * @param {Array} newTriggers - New cron triggers
   * @param {string} reason - Reason for the update
   * @returns {Promise<Object>} - Update result
   */
  async safeCronUpdate(newTriggers, reason = 'automatic') {
    const result = {
      success: false,
      message: '',
      oldTriggers: [],
      newTriggers: newTriggers || [],
      skipped: false
    };

    try {
      // Check if service is configured
      if (!this.isConfigured()) {
        result.message = 'Cloudflare API service is not configured';
        result.skipped = true;
        return result;
      }

      // Check rate limiting
      const canUpdate = await this.canUpdateNow();
      if (!canUpdate) {
        result.message = 'Rate limit: Too soon since last update';
        result.skipped = true;
        return result;
      }

      // Validate new triggers
      const validation = this.validateCronTriggers(newTriggers);
      if (!validation.valid) {
        result.message = `Validation failed: ${validation.errors.join(', ')}`;
        return result;
      }

      // Get current triggers
      result.oldTriggers = await this.getCurrentCronTriggers();

      // Check if triggers are actually different (improved comparison)
      const triggersChanged = this.areTriggersChanged(result.oldTriggers, newTriggers);
      if (!triggersChanged) {
        result.message = 'No changes needed: triggers are identical';
        result.skipped = true;
        result.success = true;
        return result;
      }

      // Perform the update
      const updateSuccess = await this.updateCronTriggers(newTriggers);

      result.success = updateSuccess;
      result.message = updateSuccess ? 'Cron triggers updated successfully' : 'Failed to update cron triggers';

      // Record the update attempt
      await this.recordCronUpdate(result.oldTriggers, newTriggers, updateSuccess, reason);

      return result;
    } catch (error) {
      result.message = `Update failed: ${error.message}`;

      // Record the failed attempt
      await this.recordCronUpdate(result.oldTriggers, newTriggers, false, reason);

      console.error('Safe cron update failed:', error);
      return result;
    }
  }

  /**
   * 改进的触发器比较方法
   * @param {Array} oldTriggers - 旧触发器数组
   * @param {Array} newTriggers - 新触发器数组
   * @returns {boolean} - 是否有变化
   */
  areTriggersChanged(oldTriggers, newTriggers) {
    // 处理空数组情况
    if (!oldTriggers || !newTriggers) {
      return true;
    }

    // 长度不同
    if (oldTriggers.length !== newTriggers.length) {
      return true;
    }

    // 逐个比较触发器（忽略空白字符差异）
    for (let i = 0; i < oldTriggers.length; i++) {
      const oldTrigger = (oldTriggers[i] || '').trim();
      const newTrigger = (newTriggers[i] || '').trim();

      if (oldTrigger !== newTrigger) {
        return true;
      }
    }

    return false;
  }
}
