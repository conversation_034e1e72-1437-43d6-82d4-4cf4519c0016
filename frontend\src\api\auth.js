import apiClient from './client'

export const authApi = {
  /**
   * Login with password
   * @param {string} password - User password
   * @returns {Promise} - Login response
   */
  async login(password) {
    const response = await apiClient.post('/auth/login', { password })
    return response.data
  },

  /**
   * Verify current token
   * @returns {Promise} - Verification response
   */
  async verify() {
    const response = await apiClient.post('/auth/verify')
    return response.data
  },

  /**
   * Logout user
   * @returns {Promise} - Logout response
   */
  async logout() {
    const response = await apiClient.post('/auth/logout')
    return response.data
  },

  /**
   * Get authentication status
   * @returns {Promise} - Auth status response
   */
  async getStatus() {
    const response = await apiClient.get('/auth/status')
    return response.data
  }
}
