/**
 * Email Executor
 * Note: This is a basic implementation. For production use, integrate with email services like:
 * - Cloudflare Email Routing
 * - SendGrid
 * - Mailgun
 * - AWS SES
 */

export const EmailExecutor = {
  type: 'email',
  name: 'Email Notification',
  description: 'Sends email notifications (requires email service configuration)',
  icon: 'mail',

  /**
   * Configuration schema for frontend form generation
   */
  configSchema: {
    type: 'object',
    required: ['to', 'subject', 'body'],
    properties: {
      to: {
        type: 'array',
        title: 'Recipients',
        description: 'Email addresses to send to',
        items: {
          type: 'string',
          format: 'email'
        },
        minItems: 1
      },
      cc: {
        type: 'array',
        title: 'CC Recipients',
        description: 'Email addresses to CC',
        items: {
          type: 'string',
          format: 'email'
        }
      },
      bcc: {
        type: 'array',
        title: 'BCC Recipients',
        description: 'Email addresses to BCC',
        items: {
          type: 'string',
          format: 'email'
        }
      },
      subject: {
        type: 'string',
        title: 'Subject',
        description: 'Email subject line'
      },
      body: {
        type: 'string',
        title: 'Body',
        description: 'Email body content',
        format: 'textarea'
      },
      isHtml: {
        type: 'boolean',
        title: 'HTML Content',
        description: 'Whether the body contains HTML',
        default: false
      },
      from: {
        type: 'string',
        title: 'From Address',
        description: 'Sender email address (optional, uses default if not specified)',
        format: 'email'
      },
      replyTo: {
        type: 'string',
        title: 'Reply To',
        description: 'Reply-to email address',
        format: 'email'
      },
      priority: {
        type: 'string',
        title: 'Priority',
        description: 'Email priority level',
        enum: ['low', 'normal', 'high'],
        default: 'normal'
      }
    }
  },

  /**
   * Validates configuration
   * @param {Object} config - Configuration to validate
   * @returns {Object} - Validation result
   */
  validateConfig(config) {
    const errors = [];

    // Validate recipients
    if (!config.to || !Array.isArray(config.to) || config.to.length === 0) {
      errors.push('At least one recipient is required');
    } else {
      for (const email of config.to) {
        if (!this.isValidEmail(email)) {
          errors.push(`Invalid email address: ${email}`);
        }
      }
    }

    // Validate CC recipients
    if (config.cc && Array.isArray(config.cc)) {
      for (const email of config.cc) {
        if (!this.isValidEmail(email)) {
          errors.push(`Invalid CC email address: ${email}`);
        }
      }
    }

    // Validate BCC recipients
    if (config.bcc && Array.isArray(config.bcc)) {
      for (const email of config.bcc) {
        if (!this.isValidEmail(email)) {
          errors.push(`Invalid BCC email address: ${email}`);
        }
      }
    }

    // Validate subject
    if (!config.subject || config.subject.trim().length === 0) {
      errors.push('Subject is required');
    }

    // Validate body
    if (!config.body || config.body.trim().length === 0) {
      errors.push('Email body is required');
    }

    // Validate from address
    if (config.from && !this.isValidEmail(config.from)) {
      errors.push(`Invalid from email address: ${config.from}`);
    }

    // Validate reply-to address
    if (config.replyTo && !this.isValidEmail(config.replyTo)) {
      errors.push(`Invalid reply-to email address: ${config.replyTo}`);
    }

    return {
      valid: errors.length === 0,
      errors
    };
  },

  /**
   * Executes the email sending
   * @param {Object} config - Task configuration
   * @param {Object} env - Environment bindings
   * @returns {Promise<Object>} - Execution result
   */
  async execute(config, env) {
    const startTime = Date.now();
    
    try {
      // Check if email service is configured
      if (!env.EMAIL_SERVICE_URL || !env.EMAIL_API_KEY) {
        throw new Error('Email service not configured. Please set EMAIL_SERVICE_URL and EMAIL_API_KEY environment variables.');
      }

      // Prepare email data
      const emailData = {
        to: config.to,
        cc: config.cc || [],
        bcc: config.bcc || [],
        subject: config.subject,
        body: config.body,
        isHtml: config.isHtml || false,
        from: config.from || env.DEFAULT_FROM_EMAIL || '<EMAIL>',
        replyTo: config.replyTo,
        priority: config.priority || 'normal',
        timestamp: new Date().toISOString()
      };

      console.log(`Sending email to ${config.to.join(', ')}: ${config.subject}`);

      // Send email via configured service
      const result = await this.sendViaEmailService(emailData, env);

      const endTime = Date.now();
      const duration = endTime - startTime;

      console.log(`Email sent successfully in ${duration}ms`);

      return {
        success: true,
        request: {
          to: emailData.to,
          subject: emailData.subject,
          from: emailData.from
        },
        response: result,
        timing: {
          startTime: new Date(startTime).toISOString(),
          endTime: new Date(endTime).toISOString(),
          duration
        }
      };

    } catch (error) {
      const endTime = Date.now();
      const duration = endTime - startTime;

      console.error('Email execution failed:', error);

      throw new Error(`Email execution failed: ${error.message}`);
    }
  },

  /**
   * Sends email via configured email service
   * @param {Object} emailData - Email data
   * @param {Object} env - Environment bindings
   * @returns {Promise<Object>} - Service response
   */
  async sendViaEmailService(emailData, env) {
    // This is a placeholder implementation
    // In production, integrate with actual email services:
    
    // Example for SendGrid:
    // const response = await fetch('https://api.sendgrid.com/v3/mail/send', {
    //   method: 'POST',
    //   headers: {
    //     'Authorization': `Bearer ${env.EMAIL_API_KEY}`,
    //     'Content-Type': 'application/json'
    //   },
    //   body: JSON.stringify({
    //     personalizations: [{
    //       to: emailData.to.map(email => ({ email })),
    //       cc: emailData.cc.map(email => ({ email })),
    //       bcc: emailData.bcc.map(email => ({ email }))
    //     }],
    //     from: { email: emailData.from },
    //     subject: emailData.subject,
    //     content: [{
    //       type: emailData.isHtml ? 'text/html' : 'text/plain',
    //       value: emailData.body
    //     }]
    //   })
    // });

    // For now, simulate email sending
    console.log('Simulating email send:', emailData);
    
    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 100));
    
    return {
      messageId: `sim_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      status: 'sent',
      timestamp: new Date().toISOString()
    };
  },

  /**
   * Validates email address format
   * @param {string} email - Email address to validate
   * @returns {boolean} - Whether email is valid
   */
  isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }
};
