<template>
  <div class="dashboard-container">
    <!-- Page Header -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">
          <el-icon><Monitor /></el-icon>
          {{ $t('dashboard.title') }}
        </h1>
        <div class="header-actions">
          <el-button :icon="Refresh" @click="refreshData" :loading="loading">
            {{ $t('common.refresh') }}
          </el-button>
        </div>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="loading && !dashboardData" class="loading-container">
      <el-icon class="is-loading"><Loading /></el-icon>
      <span class="loading-text">{{ $t('dashboard.loadingText') }}</span>
    </div>

    <!-- Dashboard Content -->
    <div v-else-if="dashboardData" class="dashboard-content">
      <!-- Overview Cards -->
      <div class="overview-section">
        <div class="overview-cards">
          <div class="overview-card">
            <div class="card-icon total">
              <el-icon><Collection /></el-icon>
            </div>
            <div class="card-content">
              <div class="card-value">{{ dashboardData.overview.totalTasks }}</div>
              <div class="card-label">{{ $t('dashboard.overview.totalTasks') }}</div>
            </div>
          </div>

          <div class="overview-card">
            <div class="card-icon active">
              <el-icon><VideoPlay /></el-icon>
            </div>
            <div class="card-content">
              <div class="card-value">{{ dashboardData.overview.activeTasks }}</div>
              <div class="card-label">{{ $t('dashboard.overview.activeTasks') }}</div>
            </div>
          </div>

          <div class="overview-card">
            <div class="card-icon health">
              <el-icon><CircleCheck /></el-icon>
            </div>
            <div class="card-content">
              <div class="card-value">{{ dashboardData.overview.healthScore }}%</div>
              <div class="card-label">{{ $t('dashboard.overview.healthScore') }}</div>
            </div>
          </div>

          <div class="overview-card">
            <div class="card-icon failures">
              <el-icon><Warning /></el-icon>
            </div>
            <div class="card-content">
              <div class="card-value">{{ dashboardData.recentFailures.length }}</div>
              <div class="card-label">{{ $t('dashboard.overview.recentFailures') }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- Main Content Grid -->
      <div class="content-grid">
        <!-- Upcoming Tasks -->
        <el-card class="upcoming-tasks-card">
          <template #header>
            <div class="card-header">
              <span>{{ $t('dashboard.upcomingTasks.title') }}</span>
              <el-icon><Timer /></el-icon>
            </div>
          </template>

          <div v-if="dashboardData.upcomingTasks.length === 0" class="empty-state">
            <el-icon class="empty-icon"><Timer /></el-icon>
            <div class="empty-title">{{ $t('dashboard.upcomingTasks.noTasks') }}</div>
            <div class="empty-description">{{ $t('dashboard.upcomingTasks.noTasksDesc') }}</div>
          </div>

          <div v-else class="upcoming-list">
            <div
              v-for="task in dashboardData.upcomingTasks"
              :key="task.id"
              class="upcoming-item"
            >
              <div class="task-info">
                <div class="task-name">{{ task.name }}</div>
                <div class="task-type">{{ task.type }}</div>
              </div>
              <div class="task-time">
                <div class="next-run">{{ formatNextRun(task.nextRunAt) }}</div>
                <div class="time-until">{{ formatTimeUntil(task.timeUntilRun) }}</div>
              </div>
            </div>
          </div>
        </el-card>

        <!-- Recent Failures -->
        <el-card class="recent-failures-card">
          <template #header>
            <div class="card-header">
              <span>{{ $t('dashboard.recentFailures.title') }}</span>
              <el-icon><Warning /></el-icon>
            </div>
          </template>

          <div v-if="dashboardData.recentFailures.length === 0" class="empty-state">
            <el-icon class="empty-icon"><CircleCheck /></el-icon>
            <div class="empty-title">{{ $t('dashboard.recentFailures.noFailures') }}</div>
            <div class="empty-description">{{ $t('dashboard.recentFailures.noFailuresDesc') }}</div>
          </div>

          <div v-else class="failures-list">
            <div
              v-for="failure in dashboardData.recentFailures"
              :key="failure.id"
              class="failure-item"
            >
              <div class="failure-info">
                <div class="failure-task">{{ failure.taskName }}</div>
                <div class="failure-message">{{ failure.message }}</div>
              </div>
              <div class="failure-meta">
                <div class="failure-time">{{ formatTime(failure.timestamp) }}</div>
                <el-tag v-if="failure.retryCount > 0" size="small" type="warning">
                  {{ $t('dashboard.recentFailures.retry') }} {{ failure.retryCount }}
                </el-tag>
              </div>
            </div>
          </div>
        </el-card>

        <!-- System Status -->
        <el-card class="system-status-card">
          <template #header>
            <div class="card-header">
              <span>{{ $t('dashboard.systemStatus.title') }}</span>
              <el-icon><Monitor /></el-icon>
            </div>
          </template>

          <div class="status-grid">
            <div class="status-item">
              <div class="status-label">{{ $t('dashboard.systemStatus.scheduler') }}</div>
              <div class="status-value">
                <el-tag
                  :type="getSchedulerStatus().type"
                  size="small"
                >
                  {{ getSchedulerStatus().text }}
                </el-tag>
              </div>
            </div>

            <div class="status-item">
              <div class="status-label">{{ $t('dashboard.systemStatus.lastRun') }}</div>
              <div class="status-value">
                {{ formatTime(dashboardData.systemStatus.lastSchedulerRun) }}
              </div>
            </div>

            <div class="status-item">
              <div class="status-label">{{ $t('dashboard.systemStatus.uptime') }}</div>
              <div class="status-value">
                {{ formatUptime(dashboardData.systemStatus.uptime) }}
              </div>
            </div>

            <div class="status-item">
              <div class="status-label">{{ $t('dashboard.systemStatus.totalRuns') }}</div>
              <div class="status-value">
                {{ dashboardData.systemStatus.schedulerRuns || 0 }}
              </div>
            </div>
          </div>
        </el-card>

        <!-- Tasks by Type -->
        <el-card class="tasks-by-type-card">
          <template #header>
            <div class="card-header">
              <span>{{ $t('dashboard.tasksByType.title') }}</span>
              <el-icon><PieChart /></el-icon>
            </div>
          </template>

          <div v-if="Object.keys(dashboardData.tasksByType).length === 0" class="empty-state">
            <el-icon class="empty-icon"><PieChart /></el-icon>
            <div class="empty-title">{{ $t('dashboard.tasksByType.noTasks') }}</div>
            <div class="empty-description">{{ $t('dashboard.tasksByType.noTasksDesc') }}</div>
          </div>

          <div v-else class="type-stats">
            <div
              v-for="(count, type) in dashboardData.tasksByType"
              :key="type"
              class="type-item"
            >
              <div class="type-info">
                <div class="type-name">{{ formatTaskType(type) }}</div>
                <div class="type-count">{{ count }} task{{ count !== 1 ? 's' : '' }}</div>
              </div>
              <div class="type-bar">
                <div
                  class="type-progress"
                  :style="{ width: `${(count / dashboardData.overview.totalTasks) * 100}%` }"
                ></div>
              </div>
            </div>
          </div>
        </el-card>
      </div>

      <!-- Alerts -->
      <div v-if="dashboardData.alerts.length > 0" class="alerts-section">
        <h3 class="section-title">{{ $t('dashboard.alerts.title') }}</h3>
        <div class="alerts-list">
          <el-alert
            v-for="alert in dashboardData.alerts"
            :key="alert.timestamp"
            :title="alert.title"
            :description="alert.message"
            :type="alert.type"
            show-icon
            :closable="false"
          />
        </div>
      </div>
    </div>

    <!-- Error State -->
    <div v-else class="error-state">
      <el-icon class="error-icon"><Warning /></el-icon>
      <div class="error-title">{{ $t('dashboard.errorTitle') }}</div>
      <div class="error-description">{{ $t('dashboard.errorDesc') }}</div>
      <el-button type="primary" @click="refreshData">{{ $t('dashboard.retry') }}</el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { useQuery } from '@tanstack/vue-query'
import { dashboardApi } from '@/api/dashboard'
import {
  Monitor, Refresh, Loading, Collection, VideoPlay,
  CircleCheck, Warning, Timer, PieChart
} from '@element-plus/icons-vue'
import dayjs from '@/utils/dayjs'

const { t } = useI18n()

// Data fetching
const { data: dashboardData, isLoading: loading, refetch } = useQuery({
  queryKey: ['dashboard'],
  queryFn: () => dashboardApi.getDashboard().then(res => res.data),
  refetchInterval: 30000, // Refresh every 30 seconds
  staleTime: 10000 // Consider data stale after 10 seconds
})

// Methods
const refreshData = () => {
  refetch()
}

const formatTime = (timestamp) => {
  if (!timestamp) return t('time.never')
  return dayjs(timestamp).format('M月D日 HH:mm')
}

const formatNextRun = (timestamp) => {
  if (!timestamp) return t('time.notScheduled')
  return dayjs(timestamp).format('HH:mm')
}

const formatTimeUntil = (milliseconds) => {
  if (!milliseconds || milliseconds <= 0) return t('time.now')

  const minutes = Math.floor(milliseconds / 60000)
  const hours = Math.floor(minutes / 60)
  const days = Math.floor(hours / 24)

  if (days > 0) return t('time.inDays', { count: days })
  if (hours > 0) return t('time.inHours', { count: hours })
  if (minutes > 0) return t('time.inMinutes', { count: minutes })
  return t('time.lessThanMinute')
}

const formatUptime = (milliseconds) => {
  if (!milliseconds) return t('time.unknown')

  const seconds = Math.floor(milliseconds / 1000)
  const minutes = Math.floor(seconds / 60)
  const hours = Math.floor(minutes / 60)
  const days = Math.floor(hours / 24)

  if (days > 0) return `${days}天 ${hours % 24}小时`
  if (hours > 0) return `${hours}小时 ${minutes % 60}分钟`
  if (minutes > 0) return `${minutes}分钟`
  return `${seconds}秒`
}

const formatTaskType = (type) => {
  return type.split('_').map(word =>
    word.charAt(0).toUpperCase() + word.slice(1)
  ).join(' ')
}

const getSchedulerStatus = () => {
  if (!dashboardData.value?.systemStatus.lastSchedulerRun) {
    return { type: 'danger', text: t('dashboard.systemStatus.notRunning') }
  }

  const lastRun = dayjs(dashboardData.value.systemStatus.lastSchedulerRun)
  const minutesAgo = dayjs().diff(lastRun, 'minute')

  if (minutesAgo > 10) {
    return { type: 'danger', text: t('dashboard.systemStatus.stalled') }
  } else if (minutesAgo > 5) {
    return { type: 'warning', text: t('dashboard.systemStatus.delayed') }
  } else {
    return { type: 'success', text: t('dashboard.systemStatus.running') }
  }
}

// Auto-refresh on mount
onMounted(() => {
  refreshData()
})
</script>

<style lang="scss" scoped>
.dashboard-container {
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: var(--app-spacing-xl);
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.page-title {
  display: flex;
  align-items: center;
  gap: var(--app-spacing-sm);
  margin: 0;
  font-size: 28px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.overview-section {
  margin-bottom: var(--app-spacing-xl);
}

.overview-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--app-spacing-lg);
}

.overview-card {
  background: var(--el-bg-color);
  border: 1px solid var(--el-border-color-light);
  border-radius: var(--app-border-radius);
  padding: var(--app-spacing-lg);
  display: flex;
  align-items: center;
  gap: var(--app-spacing-md);
  box-shadow: var(--app-shadow-light);
  transition: box-shadow 0.3s ease;

  &:hover {
    box-shadow: var(--app-shadow-medium);
  }
}

.card-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;

  &.total {
    background: var(--el-color-info-light-8);
    color: var(--el-color-info);
  }

  &.active {
    background: var(--el-color-success-light-8);
    color: var(--el-color-success);
  }

  &.health {
    background: var(--el-color-primary-light-8);
    color: var(--el-color-primary);
  }

  &.failures {
    background: var(--el-color-warning-light-8);
    color: var(--el-color-warning);
  }
}

.card-content {
  flex: 1;
}

.card-value {
  font-size: 24px;
  font-weight: 700;
  color: var(--el-text-color-primary);
  line-height: 1;
}

.card-label {
  font-size: 14px;
  color: var(--el-text-color-secondary);
  margin-top: 4px;
}

.content-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--app-spacing-lg);
  margin-bottom: var(--app-spacing-xl);
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-weight: 600;
}

.upcoming-list,
.failures-list {
  max-height: 300px;
  overflow-y: auto;
}

.upcoming-item,
.failure-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--app-spacing-md);
  border-bottom: 1px solid var(--el-border-color-lighter);

  &:last-child {
    border-bottom: none;
  }
}

.task-info,
.failure-info {
  flex: 1;
}

.task-name,
.failure-task {
  font-weight: 500;
  color: var(--el-text-color-primary);
}

.task-type,
.failure-message {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  margin-top: 2px;
}

.task-time,
.failure-meta {
  text-align: right;
}

.next-run,
.failure-time {
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

.time-until {
  font-size: 10px;
  color: var(--el-text-color-placeholder);
}

.status-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--app-spacing-md);
}

.status-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.status-label {
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

.status-value {
  font-weight: 500;
  color: var(--el-text-color-primary);
}

.type-stats {
  display: flex;
  flex-direction: column;
  gap: var(--app-spacing-md);
}

.type-item {
  display: flex;
  align-items: center;
  gap: var(--app-spacing-md);
}

.type-info {
  flex: 1;
}

.type-name {
  font-weight: 500;
  color: var(--el-text-color-primary);
}

.type-count {
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

.type-bar {
  width: 60px;
  height: 6px;
  background: var(--el-fill-color-light);
  border-radius: 3px;
  overflow: hidden;
}

.type-progress {
  height: 100%;
  background: var(--el-color-primary);
  transition: width 0.3s ease;
}

.alerts-section {
  margin-bottom: var(--app-spacing-xl);
}

.section-title {
  margin: 0 0 var(--app-spacing-md) 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.alerts-list {
  display: flex;
  flex-direction: column;
  gap: var(--app-spacing-md);
}

// Responsive design
@media (max-width: 768px) {
  .overview-cards {
    grid-template-columns: repeat(2, 1fr);
  }

  .content-grid {
    grid-template-columns: 1fr;
  }

  .status-grid {
    grid-template-columns: 1fr;
  }
}
</style>
