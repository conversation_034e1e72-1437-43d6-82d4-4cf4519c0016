<template>
  <div class="not-found-container">
    <div class="not-found-content">
      <div class="error-illustration">
        <div class="error-code">404</div>
        <div class="error-icon">
          <el-icon><QuestionFilled /></el-icon>
        </div>
      </div>

      <div class="error-text">
        <h1 class="error-title">{{ $t('notFound.title') }}</h1>
        <p class="error-description">
          {{ $t('notFound.description') }}
        </p>
      </div>

      <div class="error-actions">
        <el-button type="primary" size="large" @click="goHome">
          <el-icon><HomeFilled /></el-icon>
          {{ $t('notFound.goToDashboard') }}
        </el-button>
        <el-button size="large" @click="goBack">
          <el-icon><ArrowLeft /></el-icon>
          {{ $t('notFound.goBack') }}
        </el-button>
      </div>

      <div class="helpful-links">
        <h3>{{ $t('notFound.helpfulLinksTitle') }}</h3>
        <div class="links-grid">
          <router-link to="/dashboard" class="help-link">
            <el-icon><Monitor /></el-icon>
            <span>{{ $t('nav.dashboard') }}</span>
          </router-link>
          <router-link to="/tasks" class="help-link">
            <el-icon><Timer /></el-icon>
            <span>{{ $t('nav.tasks') }}</span>
          </router-link>
          <router-link to="/logs" class="help-link">
            <el-icon><Document /></el-icon>
            <span>{{ $t('nav.logs') }}</span>
          </router-link>
        </div>
      </div>
    </div>

    <!-- Background decoration -->
    <div class="background-decoration">
      <div class="floating-shape shape-1"></div>
      <div class="floating-shape shape-2"></div>
      <div class="floating-shape shape-3"></div>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'
import {
  QuestionFilled, HomeFilled, ArrowLeft,
  Monitor, Timer, Document
} from '@element-plus/icons-vue'

const router = useRouter()

const goHome = () => {
  router.push('/dashboard')
}

const goBack = () => {
  if (window.history.length > 1) {
    router.go(-1)
  } else {
    router.push('/dashboard')
  }
}
</script>

<style lang="scss" scoped>
.not-found-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: var(--app-spacing-lg);
  position: relative;
  overflow: hidden;
}

.not-found-content {
  text-align: center;
  max-width: 600px;
  position: relative;
  z-index: 10;
}

.error-illustration {
  position: relative;
  margin-bottom: var(--app-spacing-xl);
}

.error-code {
  font-size: 120px;
  font-weight: 900;
  color: var(--el-color-primary);
  line-height: 1;
  margin-bottom: var(--app-spacing-md);
  background: linear-gradient(135deg, var(--el-color-primary), #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.error-icon {
  font-size: 48px;
  color: var(--el-color-primary-light-3);
  position: absolute;
  top: 20px;
  right: 20px;
  animation: bounce 2s infinite;
}

.error-text {
  margin-bottom: var(--app-spacing-xl);
}

.error-title {
  font-size: 32px;
  font-weight: 700;
  color: var(--el-text-color-primary);
  margin: 0 0 var(--app-spacing-md) 0;
}

.error-description {
  font-size: 16px;
  color: var(--el-text-color-secondary);
  line-height: 1.6;
  margin: 0;
}

.error-actions {
  display: flex;
  gap: var(--app-spacing-md);
  justify-content: center;
  margin-bottom: var(--app-spacing-xl);
  flex-wrap: wrap;
}

.helpful-links {
  background: var(--el-bg-color);
  border-radius: 16px;
  padding: var(--app-spacing-xl);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);

  h3 {
    margin: 0 0 var(--app-spacing-lg) 0;
    font-size: 18px;
    font-weight: 600;
    color: var(--el-text-color-primary);
  }
}

.links-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: var(--app-spacing-md);
}

.help-link {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--app-spacing-sm);
  padding: var(--app-spacing-lg);
  background: var(--el-fill-color-light);
  border-radius: 12px;
  text-decoration: none;
  color: var(--el-text-color-primary);
  transition: all 0.3s ease;

  &:hover {
    background: var(--el-color-primary-light-9);
    color: var(--el-color-primary);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .el-icon {
    font-size: 24px;
  }

  span {
    font-weight: 500;
  }
}

// Background decoration
.background-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.floating-shape {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 8s ease-in-out infinite;
}

.shape-1 {
  width: 100px;
  height: 100px;
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.shape-2 {
  width: 150px;
  height: 150px;
  top: 60%;
  right: 15%;
  animation-delay: 3s;
}

.shape-3 {
  width: 80px;
  height: 80px;
  bottom: 30%;
  left: 20%;
  animation-delay: 6s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.7;
  }
  50% {
    transform: translateY(-30px) rotate(180deg);
    opacity: 1;
  }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

// Responsive design
@media (max-width: 768px) {
  .error-code {
    font-size: 80px;
  }

  .error-title {
    font-size: 24px;
  }

  .error-actions {
    flex-direction: column;
    align-items: center;

    .el-button {
      width: 200px;
    }
  }

  .links-grid {
    grid-template-columns: 1fr;
  }

  .floating-shape {
    display: none;
  }
}

// Dark mode adjustments
@media (prefers-color-scheme: dark) {
  .not-found-container {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  }

  .floating-shape {
    background: rgba(255, 255, 255, 0.05);
  }
}
</style>
