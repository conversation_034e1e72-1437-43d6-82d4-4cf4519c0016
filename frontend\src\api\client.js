import axios from 'axios'
import { ElMessage } from 'element-plus'

// Create axios instance
const apiClient = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || '/api',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// Request interceptor
apiClient.interceptors.request.use(
  (config) => {
    // Add auth token if available
    const token = localStorage.getItem('auth_token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    
    // Add request timestamp
    config.metadata = { startTime: Date.now() }
    
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor
apiClient.interceptors.response.use(
  (response) => {
    // Calculate request duration
    const duration = Date.now() - response.config.metadata.startTime
    console.log(`API Request: ${response.config.method?.toUpperCase()} ${response.config.url} - ${duration}ms`)
    
    return response
  },
  (error) => {
    const { response, config } = error
    
    // Calculate request duration
    const duration = config?.metadata ? Date.now() - config.metadata.startTime : 0
    console.error(`API Error: ${config?.method?.toUpperCase()} ${config?.url} - ${duration}ms`, error)
    
    // Handle different error types
    if (response) {
      const { status, data } = response
      
      switch (status) {
        case 401:
          // Unauthorized - clear token and redirect to login
          localStorage.removeItem('auth_token')
          localStorage.removeItem('user_info')
          
          // Only show message if not already on login page
          if (!window.location.pathname.includes('/login')) {
            ElMessage.error('Session expired. Please login again.')
            window.location.href = '/login'
          }
          break
          
        case 403:
          ElMessage.error('Access denied')
          break
          
        case 404:
          ElMessage.error('Resource not found')
          break
          
        case 429:
          ElMessage.error('Too many requests. Please try again later.')
          break
          
        case 500:
          ElMessage.error('Server error. Please try again later.')
          break
          
        default:
          // Show error message from server if available
          const message = data?.message || data?.error || 'An error occurred'
          ElMessage.error(message)
      }
    } else if (error.code === 'ECONNABORTED') {
      ElMessage.error('Request timeout. Please check your connection.')
    } else if (error.message === 'Network Error') {
      ElMessage.error('Network error. Please check your connection.')
    } else {
      ElMessage.error('An unexpected error occurred')
    }
    
    return Promise.reject(error)
  }
)

export default apiClient
