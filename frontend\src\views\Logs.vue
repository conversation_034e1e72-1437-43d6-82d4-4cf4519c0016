<template>
  <div class="logs-container">
    <!-- Page Header -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">
          <el-icon><Document /></el-icon>
          {{ $t('logs.title') }}
        </h1>
        <div class="header-actions">
          <el-button :icon="Refresh" @click="refreshLogs" :loading="loading">
            {{ $t('common.refresh') }}
          </el-button>
          <el-button :icon="Download" @click="exportLogs">
            {{ $t('common.export') }}
          </el-button>
          <el-button :icon="Delete" type="danger" @click="clearLogs">
            {{ $t('common.clearAll') }}
          </el-button>
        </div>
      </div>
    </div>

    <!-- Filters and Stats -->
    <div class="controls-section">
      <el-row :gutter="16">
        <!-- Filters -->
        <el-col :span="16">
          <el-card class="filters-card">
            <div class="filters-content">
              <el-select
                v-model="filters.type"
                placeholder="Filter by type"
                clearable
                style="width: 150px"
                @change="applyFilters"
              >
                <el-option label="All" value="" />
                <el-option label="Failures Only" value="failure" />
                <el-option label="Success Only" value="success" />
              </el-select>

              <el-select
                v-model="filters.level"
                placeholder="Filter by level"
                clearable
                style="width: 120px"
                @change="applyFilters"
              >
                <el-option label="Error" value="error" />
                <el-option label="Warning" value="warn" />
                <el-option label="Info" value="info" />
                <el-option label="Debug" value="debug" />
              </el-select>

              <el-select
                v-model="filters.taskId"
                placeholder="Filter by task"
                clearable
                filterable
                style="width: 200px"
                @change="applyFilters"
              >
                <el-option
                  v-for="task in availableTasks"
                  :key="task.id"
                  :label="task.name"
                  :value="task.id"
                />
              </el-select>

              <el-input-number
                v-model="filters.limit"
                :min="10"
                :max="100"
                :step="10"
                style="width: 120px"
                @change="applyFilters"
              />

              <el-button @click="clearFilters">Clear Filters</el-button>
            </div>
          </el-card>
        </el-col>

        <!-- Quick Stats -->
        <el-col :span="8">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stat-item">
                <span class="stat-label">Total Logs:</span>
                <span class="stat-value">{{ logs.length }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">Failures:</span>
                <span class="stat-value error">{{ failureCount }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">Success Rate:</span>
                <span class="stat-value success">{{ successRate }}%</span>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- Logs Table -->
    <el-card class="logs-table-card">
      <div v-if="loading && logs.length === 0" class="loading-container">
        <el-icon class="is-loading"><Loading /></el-icon>
        <span class="loading-text">Loading logs...</span>
      </div>

      <div v-else-if="logs.length === 0" class="empty-state">
        <el-icon class="empty-icon"><Document /></el-icon>
        <div class="empty-title">No logs found</div>
        <div class="empty-description">
          No execution logs match your current filters
        </div>
      </div>

      <el-table
        v-else
        :data="logs"
        class="logs-table"
        stripe
        @row-click="showLogDetails"
      >
        <el-table-column prop="timestamp" label="Time" width="160">
          <template #default="{ row }">
            <div class="timestamp">
              <div class="time">{{ formatTime(row.timestamp) }}</div>
              <div class="time-ago">{{ row.timeAgo }}</div>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="taskName" label="Task" min-width="200">
          <template #default="{ row }">
            <div class="task-info">
              <span class="task-name">{{ row.taskName || 'Unknown Task' }}</span>
              <div v-if="row.taskId" class="task-id">{{ row.taskId.slice(0, 8) }}...</div>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="level" label="Level" width="80">
          <template #default="{ row }">
            <el-tag
              :class="`level-${row.level}`"
              size="small"
            >
              {{ row.level.toUpperCase() }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="status" label="Status" width="100">
          <template #default="{ row }">
            <el-tag
              :type="getStatusType(row.status)"
              size="small"
            >
              {{ formatStatus(row.status) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="message" label="Message" min-width="300">
          <template #default="{ row }">
            <div class="message-content">
              <span class="message-text">{{ row.message }}</span>
              <div v-if="row.retryCount > 0" class="retry-info">
                <el-tag size="small" type="warning">
                  Retry {{ row.retryCount }}
                </el-tag>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="executionTime" label="Duration" width="100">
          <template #default="{ row }">
            <span v-if="row.executionTime" class="execution-time">
              {{ formatDuration(row.executionTime) }}
            </span>
            <span v-else class="no-duration">-</span>
          </template>
        </el-table-column>

        <el-table-column label="Actions" width="80" fixed="right">
          <template #default="{ row }">
            <el-tooltip content="View Details">
              <el-button
                :icon="View"
                circle
                size="small"
                @click.stop="showLogDetails(row)"
              />
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- Log Details Dialog -->
    <el-dialog
      v-model="detailsVisible"
      title="Log Details"
      width="800px"
      :close-on-click-modal="false"
    >
      <div v-if="selectedLog" class="log-details">
        <!-- Basic Info -->
        <div class="details-section">
          <h4>Basic Information</h4>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="Timestamp">
              {{ formatFullTime(selectedLog.timestamp) }}
            </el-descriptions-item>
            <el-descriptions-item label="Task">
              {{ selectedLog.taskName || 'Unknown' }}
            </el-descriptions-item>
            <el-descriptions-item label="Level">
              <el-tag :class="`level-${selectedLog.level}`" size="small">
                {{ selectedLog.level.toUpperCase() }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="Status">
              <el-tag :type="getStatusType(selectedLog.status)" size="small">
                {{ formatStatus(selectedLog.status) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="Execution Time">
              {{ selectedLog.executionTime ? formatDuration(selectedLog.executionTime) : 'N/A' }}
            </el-descriptions-item>
            <el-descriptions-item label="Retry Count">
              {{ selectedLog.retryCount || 0 }}
            </el-descriptions-item>
          </el-descriptions>
        </div>

        <!-- Message -->
        <div class="details-section">
          <h4>Message</h4>
          <div class="message-box">
            {{ selectedLog.message }}
          </div>
        </div>

        <!-- Details -->
        <div v-if="selectedLog.details && Object.keys(selectedLog.details).length > 0" class="details-section">
          <h4>Execution Details</h4>
          <el-collapse>
            <el-collapse-item
              v-for="(value, key) in selectedLog.details"
              :key="key"
              :title="formatDetailKey(key)"
            >
              <pre class="detail-content">{{ JSON.stringify(value, null, 2) }}</pre>
            </el-collapse-item>
          </el-collapse>
        </div>
      </div>
    </el-dialog>

    <!-- Export Dialog -->
    <el-dialog v-model="exportVisible" title="Export Logs" width="400px">
      <el-form :model="exportOptions" label-position="top">
        <el-form-item label="Format">
          <el-radio-group v-model="exportOptions.format">
            <el-radio label="json">JSON</el-radio>
            <el-radio label="csv">CSV</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="Limit">
          <el-input-number
            v-model="exportOptions.limit"
            :min="1"
            :max="1000"
            style="width: 100%"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="exportVisible = false">Cancel</el-button>
        <el-button type="primary" @click="handleExport" :loading="exporting">
          Export
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useQuery } from '@tanstack/vue-query'
import { logsApi } from '@/api/logs'
import { useTasksStore } from '@/stores/tasks'
import {
  Document, Refresh, Download, Delete, Loading, View
} from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import dayjs from '@/utils/dayjs'

const tasksStore = useTasksStore()

// State
const detailsVisible = ref(false)
const exportVisible = ref(false)
const selectedLog = ref(null)
const exporting = ref(false)

// Filters
const filters = reactive({
  type: '',
  level: '',
  taskId: '',
  limit: 50
})

// Export options
const exportOptions = reactive({
  format: 'json',
  limit: 100
})

// Data fetching
const { data: logs = [], isLoading: loading, refetch } = useQuery({
  queryKey: ['logs', filters],
  queryFn: () => logsApi.getLogs(filters).then(res => res.data),
  refetchInterval: 30000 // Refresh every 30 seconds
})

// Computed
const availableTasks = computed(() => tasksStore.tasks)

const failureCount = computed(() => {
  return logs.value.filter(log =>
    log.status === 'failure' || log.status === 'timeout'
  ).length
})

const successRate = computed(() => {
  if (logs.value.length === 0) return 0
  const successCount = logs.value.filter(log => log.status === 'success').length
  return Math.round((successCount / logs.value.length) * 100)
})

// Methods
const refreshLogs = () => {
  refetch()
}

const applyFilters = () => {
  refetch()
}

const clearFilters = () => {
  Object.assign(filters, {
    type: '',
    level: '',
    taskId: '',
    limit: 50
  })
  refetch()
}

const showLogDetails = (log) => {
  selectedLog.value = log
  detailsVisible.value = true
}

const exportLogs = () => {
  exportVisible.value = true
}

const handleExport = async () => {
  try {
    exporting.value = true

    const response = await logsApi.exportLogs(exportOptions)

    if (exportOptions.format === 'csv') {
      // Handle CSV download
      const blob = new Blob([response.data], { type: 'text/csv' })
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `logs-${dayjs().format('YYYY-MM-DD')}.csv`
      link.click()
      window.URL.revokeObjectURL(url)
    } else {
      // Handle JSON download
      const blob = new Blob([JSON.stringify(response.data, null, 2)], {
        type: 'application/json'
      })
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `logs-${dayjs().format('YYYY-MM-DD')}.json`
      link.click()
      window.URL.revokeObjectURL(url)
    }

    ElMessage.success('Logs exported successfully')
    exportVisible.value = false

  } catch (error) {
    console.error('Export error:', error)
    ElMessage.error('Failed to export logs')
  } finally {
    exporting.value = false
  }
}

const clearLogs = async () => {
  try {
    await ElMessageBox.confirm(
      'This will permanently delete all logs. Continue?',
      'Warning',
      {
        confirmButtonText: 'Clear All',
        cancelButtonText: 'Cancel',
        type: 'warning'
      }
    )

    await logsApi.clearLogs()
    ElMessage.success('All logs cleared successfully')
    refetch()

  } catch (error) {
    if (error !== 'cancel') {
      console.error('Clear logs error:', error)
    }
  }
}

// Utility functions
const formatTime = (timestamp) => {
  return dayjs(timestamp).format('HH:mm:ss')
}

const formatFullTime = (timestamp) => {
  return dayjs(timestamp).format('YYYY-MM-DD HH:mm:ss')
}

const formatDuration = (ms) => {
  if (ms < 1000) return `${ms}ms`
  if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`
  return `${(ms / 60000).toFixed(1)}m`
}

const formatStatus = (status) => {
  return status.charAt(0).toUpperCase() + status.slice(1)
}

const getStatusType = (status) => {
  switch (status) {
    case 'success': return 'success'
    case 'failure': return 'danger'
    case 'timeout': return 'warning'
    case 'retry': return 'warning'
    default: return 'info'
  }
}

const formatDetailKey = (key) => {
  return key.split('_').map(word =>
    word.charAt(0).toUpperCase() + word.slice(1)
  ).join(' ')
}

// Initialize
onMounted(() => {
  tasksStore.fetchTasks()
})
</script>

<style lang="scss" scoped>
.logs-container {
  max-width: 1400px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: var(--app-spacing-lg);
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.page-title {
  display: flex;
  align-items: center;
  gap: var(--app-spacing-sm);
  margin: 0;
  font-size: 28px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.header-actions {
  display: flex;
  gap: var(--app-spacing-sm);
}

.controls-section {
  margin-bottom: var(--app-spacing-lg);
}

.filters-content {
  display: flex;
  align-items: center;
  gap: var(--app-spacing-md);
  flex-wrap: wrap;
}

.stats-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.stat-label {
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

.stat-value {
  font-size: 18px;
  font-weight: 600;

  &.error {
    color: var(--el-color-danger);
  }

  &.success {
    color: var(--el-color-success);
  }
}

.logs-table-card {
  .el-card__body {
    padding: 0;
  }
}

.logs-table {
  .timestamp {
    .time {
      font-weight: 500;
      color: var(--el-text-color-primary);
    }

    .time-ago {
      font-size: 11px;
      color: var(--el-text-color-secondary);
    }
  }

  .task-info {
    .task-name {
      font-weight: 500;
      color: var(--el-text-color-primary);
    }

    .task-id {
      font-size: 11px;
      color: var(--el-text-color-placeholder);
      font-family: monospace;
    }
  }

  .message-content {
    .message-text {
      display: block;
      margin-bottom: 4px;
    }

    .retry-info {
      display: inline-block;
    }
  }

  .execution-time {
    font-family: monospace;
    font-size: 12px;
  }

  .no-duration {
    color: var(--el-text-color-placeholder);
  }

  :deep(.el-table__row) {
    cursor: pointer;

    &:hover {
      background-color: var(--el-fill-color-light);
    }
  }
}

.log-details {
  .details-section {
    margin-bottom: var(--app-spacing-lg);

    h4 {
      margin: 0 0 var(--app-spacing-md) 0;
      font-size: 16px;
      font-weight: 600;
      color: var(--el-text-color-primary);
    }
  }

  .message-box {
    background: var(--el-fill-color-light);
    padding: var(--app-spacing-md);
    border-radius: var(--app-border-radius);
    font-family: monospace;
    white-space: pre-wrap;
    word-break: break-word;
  }

  .detail-content {
    background: var(--el-fill-color-light);
    padding: var(--app-spacing-md);
    border-radius: var(--app-border-radius);
    font-size: 12px;
    overflow-x: auto;
    margin: 0;
  }
}

// Level-specific styles
.level-error {
  background: rgba(245, 108, 108, 0.1);
  color: var(--el-color-danger);
  border-color: var(--el-color-danger);
}

.level-warn {
  background: rgba(230, 162, 60, 0.1);
  color: var(--el-color-warning);
  border-color: var(--el-color-warning);
}

.level-info {
  background: rgba(64, 158, 255, 0.1);
  color: var(--el-color-primary);
  border-color: var(--el-color-primary);
}

.level-debug {
  background: rgba(144, 147, 153, 0.1);
  color: var(--el-color-info);
  border-color: var(--el-color-info);
}

// Responsive design
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--app-spacing-md);
  }

  .header-actions {
    width: 100%;
    justify-content: flex-end;
  }

  .filters-content {
    flex-direction: column;
    align-items: stretch;

    .el-select,
    .el-input-number {
      width: 100% !important;
    }
  }

  .stats-content {
    flex-direction: column;
    gap: var(--app-spacing-sm);
  }
}
</style>
