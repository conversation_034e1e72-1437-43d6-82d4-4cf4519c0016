import { TaskService } from '../services/TaskService.js';
import { SchedulerService } from '../services/SchedulerService.js';

/**
 * Handles scheduled events (cron triggers)
 * @param {ScheduledEvent} event - The scheduled event
 * @param {Object} env - Environment bindings
 * @param {ExecutionContext} ctx - Execution context
 */
export async function handleScheduled(event, env, ctx) {
  const scheduledTime = new Date(event.scheduledTime);
  const cron = event.cron;

  console.log(`Scheduled event triggered at ${scheduledTime.toISOString()} with cron: ${cron}`);

  try {
    // Initialize services
    const taskService = new TaskService(env);
    const schedulerService = new SchedulerService(env);

    // Handle different cron triggers
    if (cron === '*/1 * * * *') {
      // Main scheduler - every minute
      await handleMainScheduler(taskService, schedulerService, scheduledTime, env, ctx);
    } else if (cron === '*/5 * * * *') {
      // Backup scheduler - every 5 minutes
      await handleBackupScheduler(taskService, schedulerService, scheduledTime, env, ctx);
    } else if (cron === '0 */1 * * *') {
      // Watchdog - every hour
      await handleWatchdog(schedulerService, scheduledTime, env, ctx);
    }

  } catch (error) {
    console.error('Scheduled handler error:', error);
    // Log error but don't throw to prevent cron retry
  }
}

/**
 * Main scheduler logic - finds and dispatches tasks
 */
async function handleMainScheduler(taskService, schedulerService, scheduledTime, env, ctx) {
  console.log('Running main scheduler...');

  try {
    // Get all active tasks that should run at this time
    const tasksToRun = await taskService.getTasksToRun(scheduledTime);

    console.log(`Found ${tasksToRun.length} tasks to run`);

    // Dispatch tasks to their respective Durable Objects
    const dispatchPromises = tasksToRun.map(task =>
      dispatchTaskToExecutor(task, env, ctx)
    );

    // Fire and forget - don't wait for task execution
    ctx.waitUntil(Promise.allSettled(dispatchPromises));

    // Update next cron trigger time based on all active tasks (not just tasksToRun)
    // This ensures we have the most current view of all tasks, not just the ones about to run
    const cronUpdateResult = await schedulerService.updateNextCronTrigger(scheduledTime, null);

    if (cronUpdateResult.cronUpdated) {
      console.log(`Cron trigger optimized: ${cronUpdateResult.optimalCron} (${tasksToRun.length} tasks)`);
    } else if (!cronUpdateResult.skipped) {
      console.log(`Cron update failed: ${cronUpdateResult.message}`);
    }

  } catch (error) {
    console.error('Main scheduler error:', error);
  }
}

/**
 * Backup scheduler - runs less frequently as a safety net
 */
async function handleBackupScheduler(taskService, schedulerService, scheduledTime, env, ctx) {
  console.log('Running backup scheduler...');

  try {
    // Check for any missed tasks in the last 5 minutes
    const fiveMinutesAgo = new Date(scheduledTime.getTime() - 5 * 60 * 1000);
    const missedTasks = await taskService.getMissedTasks(fiveMinutesAgo, scheduledTime);

    if (missedTasks.length > 0) {
      console.log(`Found ${missedTasks.length} missed tasks, dispatching...`);

      const dispatchPromises = missedTasks.map(task =>
        dispatchTaskToExecutor(task, env, ctx)
      );

      ctx.waitUntil(Promise.allSettled(dispatchPromises));
    }

  } catch (error) {
    console.error('Backup scheduler error:', error);
  }
}

/**
 * Watchdog - ensures system health and performs maintenance
 */
async function handleWatchdog(schedulerService, scheduledTime, env, ctx) {
  console.log('Running watchdog...');

  try {
    // Perform system health checks
    await schedulerService.performHealthCheck();

    // Clean up old logs if needed
    await schedulerService.cleanupOldLogs();

    // Update system metrics
    await schedulerService.updateSystemMetrics(scheduledTime);

  } catch (error) {
    console.error('Watchdog error:', error);
  }
}

/**
 * Dispatches a task to its corresponding Durable Object executor
 */
async function dispatchTaskToExecutor(task, env, ctx) {
  try {
    // Get Durable Object stub for this task
    const doId = env.TASK_EXECUTOR.idFromName(task.id);
    const doStub = env.TASK_EXECUTOR.get(doId);

    // Send execution command to the DO (fire and forget)
    const response = await doStub.fetch('https://scheduler.internal/execute', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        taskId: task.id,
        scheduledTime: new Date().toISOString()
      })
    });

    if (!response.ok) {
      console.error(`Failed to dispatch task ${task.id}: ${response.status}`);
    }

  } catch (error) {
    console.error(`Error dispatching task ${task.id}:`, error);
  }
}
