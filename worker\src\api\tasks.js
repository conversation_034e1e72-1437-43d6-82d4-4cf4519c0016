/**
 * Tasks API routes
 */

import { Hono } from 'hono';
import { TaskService } from '../services/TaskService.js';
import { ExecutorRegistry } from '../executors/registry.js';
import { createTask, validateTask } from '../schemas/task.js';
import { ValidationError, NotFoundError } from '../middleware/error.js';
// UUID generation utility
function generateUUID() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

const app = new Hono();

/**
 * GET /tasks - Get all tasks
 */
app.get('/', async (c) => {
  try {
    const taskService = new TaskService(c.env);
    const tasks = await taskService.getAllTasks();

    // Add executor metadata to tasks
    const tasksWithMetadata = tasks.map(task => {
      const executor = ExecutorRegistry.getExecutor(task.type);
      return {
        ...task,
        executorName: executor?.name || 'Unknown',
        executorIcon: executor?.icon || 'help'
      };
    });

    return c.json({
      success: true,
      data: tasksWithMetadata,
      count: tasksWithMetadata.length,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Get tasks error:', error);
    return c.json({
      error: 'Internal Server Error',
      message: 'Failed to retrieve tasks'
    }, 500);
  }
});

/**
 * GET /tasks/:id - Get task by ID
 */
app.get('/:id', async (c) => {
  try {
    const taskId = c.req.param('id');
    const taskService = new TaskService(c.env);
    const task = await taskService.getTask(taskId);

    if (!task) {
      throw new NotFoundError(`Task with ID ${taskId} not found`);
    }

    // Add executor metadata
    const executor = ExecutorRegistry.getExecutor(task.type);
    const taskWithMetadata = {
      ...task,
      executorName: executor?.name || 'Unknown',
      executorIcon: executor?.icon || 'help'
    };

    return c.json({
      success: true,
      data: taskWithMetadata,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    if (error instanceof NotFoundError) {
      throw error;
    }

    console.error('Get task error:', error);
    return c.json({
      error: 'Internal Server Error',
      message: 'Failed to retrieve task'
    }, 500);
  }
});

/**
 * POST /tasks - Create new task
 */
app.post('/', async (c) => {
  try {
    const taskData = await c.req.json();

    // Generate ID if not provided
    if (!taskData.id) {
      taskData.id = generateUUID();
    }

    // Create task with defaults
    const task = createTask(taskData);

    // Validate task
    const validation = validateTask(task);
    if (!validation.valid) {
      throw new ValidationError('Task validation failed', validation.errors);
    }

    // Validate executor configuration
    const configValidation = ExecutorRegistry.validateConfig(task.type, task.config);
    if (!configValidation.valid) {
      throw new ValidationError('Task configuration validation failed', configValidation.errors);
    }

    // Save task
    const taskService = new TaskService(c.env);
    const success = await taskService.saveTask(task);

    if (!success) {
      return c.json({
        error: 'Internal Server Error',
        message: 'Failed to create task'
      }, 500);
    }

    return c.json({
      success: true,
      data: task,
      message: 'Task created successfully',
      timestamp: new Date().toISOString()
    }, 201);

  } catch (error) {
    if (error instanceof ValidationError) {
      throw error;
    }

    console.error('Create task error:', error);
    return c.json({
      error: 'Internal Server Error',
      message: 'Failed to create task'
    }, 500);
  }
});

/**
 * PUT /tasks/:id - Update task
 */
app.put('/:id', async (c) => {
  try {
    const taskId = c.req.param('id');
    const updateData = await c.req.json();

    const taskService = new TaskService(c.env);
    const existingTask = await taskService.getTask(taskId);

    if (!existingTask) {
      throw new NotFoundError(`Task with ID ${taskId} not found`);
    }

    // Merge update data with existing task
    const updatedTask = {
      ...existingTask,
      ...updateData,
      id: taskId, // Ensure ID doesn't change
      updatedAt: new Date().toISOString()
    };

    // Validate updated task
    const validation = validateTask(updatedTask);
    if (!validation.valid) {
      throw new ValidationError('Task validation failed', validation.errors);
    }

    // Validate executor configuration
    const configValidation = ExecutorRegistry.validateConfig(updatedTask.type, updatedTask.config);
    if (!configValidation.valid) {
      throw new ValidationError('Task configuration validation failed', configValidation.errors);
    }

    // Save updated task
    const success = await taskService.saveTask(updatedTask);

    if (!success) {
      return c.json({
        error: 'Internal Server Error',
        message: 'Failed to update task'
      }, 500);
    }

    return c.json({
      success: true,
      data: updatedTask,
      message: 'Task updated successfully',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    if (error instanceof ValidationError || error instanceof NotFoundError) {
      throw error;
    }

    console.error('Update task error:', error);
    return c.json({
      error: 'Internal Server Error',
      message: 'Failed to update task'
    }, 500);
  }
});

/**
 * DELETE /tasks/:id - Delete task
 */
app.delete('/:id', async (c) => {
  try {
    const taskId = c.req.param('id');

    const taskService = new TaskService(c.env);
    const existingTask = await taskService.getTask(taskId);

    if (!existingTask) {
      throw new NotFoundError(`Task with ID ${taskId} not found`);
    }

    const success = await taskService.deleteTask(taskId);

    if (!success) {
      return c.json({
        error: 'Internal Server Error',
        message: 'Failed to delete task'
      }, 500);
    }

    return c.json({
      success: true,
      message: 'Task deleted successfully',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    if (error instanceof NotFoundError) {
      throw error;
    }

    console.error('Delete task error:', error);
    return c.json({
      error: 'Internal Server Error',
      message: 'Failed to delete task'
    }, 500);
  }
});

/**
 * POST /tasks/:id/run - Manually trigger task execution
 */
app.post('/:id/run', async (c) => {
  try {
    const taskId = c.req.param('id');

    const taskService = new TaskService(c.env);
    const task = await taskService.getTask(taskId);

    if (!task) {
      throw new NotFoundError(`Task with ID ${taskId} not found`);
    }

    // Get Durable Object stub for this task
    const doId = c.env.TASK_EXECUTOR.idFromName(taskId);
    const doStub = c.env.TASK_EXECUTOR.get(doId);

    // Send execution command to the DO
    const response = await doStub.fetch('https://manual.trigger/execute', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        taskId: taskId,
        scheduledTime: new Date().toISOString(),
        manual: true
      })
    });

    const result = await response.json();

    return c.json({
      success: true,
      data: result,
      message: 'Task execution triggered successfully',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    if (error instanceof NotFoundError) {
      throw error;
    }

    console.error('Manual task execution error:', error);
    return c.json({
      error: 'Internal Server Error',
      message: 'Failed to trigger task execution'
    }, 500);
  }
});

// Note: /types endpoint moved to fetch.js as a public route

export default app;
