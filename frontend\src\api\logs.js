import apiClient from './client'

export const logsApi = {
  /**
   * Get logs with optional filters
   * @param {Object} params - Query parameters
   * @returns {Promise} - Logs response
   */
  async getLogs(params = {}) {
    const response = await apiClient.get('/logs', { params })
    return response.data
  },

  /**
   * Get specific log entry
   * @param {string} id - Log ID
   * @returns {Promise} - Log response
   */
  async getLog(id) {
    const response = await apiClient.get(`/logs/${id}`)
    return response.data
  },

  /**
   * Get log statistics
   * @returns {Promise} - Log stats response
   */
  async getLogStats() {
    const response = await apiClient.get('/logs/stats')
    return response.data
  },

  /**
   * Export logs
   * @param {Object} params - Export parameters
   * @returns {Promise} - Export response
   */
  async exportLogs(params = {}) {
    const response = await apiClient.get('/logs/export', { 
      params,
      responseType: params.format === 'csv' ? 'blob' : 'json'
    })
    return response
  },

  /**
   * Clear all logs
   * @returns {Promise} - Clear response
   */
  async clearLogs() {
    const response = await apiClient.delete('/logs')
    return response.data
  }
}
