/**
 * Logs API routes
 */

import { Hono } from 'hono';
import { LogKV } from '../utils/kv.js';
import { ValidationError } from '../middleware/error.js';

const app = new Hono();

/**
 * GET /logs - Get recent logs
 */
app.get('/', async (c) => {
  try {
    const logKV = new LogKV(c.env.FAIL_LOGS_KV);
    
    // Get query parameters
    const limit = parseInt(c.req.query('limit')) || 50;
    const type = c.req.query('type'); // 'failure', 'success', 'all'
    const taskId = c.req.query('taskId');
    
    // Validate limit
    if (limit < 1 || limit > 100) {
      throw new ValidationError('Limit must be between 1 and 100');
    }

    let logs;
    
    if (taskId) {
      // Get logs for specific task
      logs = await logKV.getTaskLogs(taskId, limit);
    } else if (type === 'failure') {
      // Get only failure logs
      logs = await logKV.getFailureLogs(limit);
    } else {
      // Get all recent logs
      logs = await logKV.getRecentLogs(limit);
    }

    // Add additional metadata
    const logsWithMetadata = logs.map(log => ({
      ...log,
      timeAgo: getTimeAgo(new Date(log.timestamp)),
      severity: getSeverity(log.level, log.status)
    }));

    return c.json({
      success: true,
      data: logsWithMetadata,
      count: logsWithMetadata.length,
      filters: {
        limit,
        type: type || 'all',
        taskId: taskId || null
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    if (error instanceof ValidationError) {
      throw error;
    }
    
    console.error('Get logs error:', error);
    return c.json({
      error: 'Internal Server Error',
      message: 'Failed to retrieve logs'
    }, 500);
  }
});

/**
 * GET /logs/:id - Get specific log entry
 */
app.get('/:id', async (c) => {
  try {
    const logId = c.req.param('id');
    const logKV = new LogKV(c.env.FAIL_LOGS_KV);
    
    // Get all logs and find the specific one
    const allLogs = await logKV.getRecentLogs(50);
    const log = allLogs.find(l => l.id === logId);
    
    if (!log) {
      return c.json({
        error: 'Not Found',
        message: `Log entry with ID ${logId} not found`
      }, 404);
    }

    // Add metadata
    const logWithMetadata = {
      ...log,
      timeAgo: getTimeAgo(new Date(log.timestamp)),
      severity: getSeverity(log.level, log.status)
    };

    return c.json({
      success: true,
      data: logWithMetadata,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Get log error:', error);
    return c.json({
      error: 'Internal Server Error',
      message: 'Failed to retrieve log entry'
    }, 500);
  }
});

/**
 * GET /logs/stats - Get log statistics
 */
app.get('/stats', async (c) => {
  try {
    const logKV = new LogKV(c.env.FAIL_LOGS_KV);
    const logs = await logKV.getRecentLogs(100);
    
    const now = new Date();
    const stats = {
      total: logs.length,
      byStatus: {},
      byLevel: {},
      byType: {},
      byTimeRange: {
        lastHour: 0,
        last6Hours: 0,
        last24Hours: 0,
        lastWeek: 0
      },
      topFailingTasks: {},
      averageExecutionTime: 0
    };

    let totalExecutionTime = 0;
    let executionTimeCount = 0;

    // Analyze logs
    for (const log of logs) {
      const logTime = new Date(log.timestamp);
      const timeDiff = now.getTime() - logTime.getTime();
      
      // Count by status
      stats.byStatus[log.status] = (stats.byStatus[log.status] || 0) + 1;
      
      // Count by level
      stats.byLevel[log.level] = (stats.byLevel[log.level] || 0) + 1;
      
      // Count by type
      stats.byType[log.type] = (stats.byType[log.type] || 0) + 1;
      
      // Count by time range
      if (timeDiff < 60 * 60 * 1000) { // 1 hour
        stats.byTimeRange.lastHour++;
      }
      if (timeDiff < 6 * 60 * 60 * 1000) { // 6 hours
        stats.byTimeRange.last6Hours++;
      }
      if (timeDiff < 24 * 60 * 60 * 1000) { // 24 hours
        stats.byTimeRange.last24Hours++;
      }
      if (timeDiff < 7 * 24 * 60 * 60 * 1000) { // 1 week
        stats.byTimeRange.lastWeek++;
      }
      
      // Track failing tasks
      if (log.status === 'failure' || log.status === 'timeout') {
        const taskKey = `${log.taskId}:${log.taskName}`;
        stats.topFailingTasks[taskKey] = (stats.topFailingTasks[taskKey] || 0) + 1;
      }
      
      // Calculate average execution time
      if (log.executionTime && log.executionTime > 0) {
        totalExecutionTime += log.executionTime;
        executionTimeCount++;
      }
    }

    // Calculate average execution time
    if (executionTimeCount > 0) {
      stats.averageExecutionTime = Math.round(totalExecutionTime / executionTimeCount);
    }

    // Convert top failing tasks to array and sort
    stats.topFailingTasks = Object.entries(stats.topFailingTasks)
      .map(([taskKey, count]) => {
        const [taskId, taskName] = taskKey.split(':');
        return { taskId, taskName, failureCount: count };
      })
      .sort((a, b) => b.failureCount - a.failureCount)
      .slice(0, 10);

    return c.json({
      success: true,
      data: stats,
      timestamp: now.toISOString()
    });

  } catch (error) {
    console.error('Get log stats error:', error);
    return c.json({
      error: 'Internal Server Error',
      message: 'Failed to retrieve log statistics'
    }, 500);
  }
});

/**
 * DELETE /logs - Clear all logs (admin only)
 */
app.delete('/', async (c) => {
  try {
    const logKV = new LogKV(c.env.FAIL_LOGS_KV);
    const success = await logKV.clearLogs();
    
    if (!success) {
      return c.json({
        error: 'Internal Server Error',
        message: 'Failed to clear logs'
      }, 500);
    }

    return c.json({
      success: true,
      message: 'All logs cleared successfully',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Clear logs error:', error);
    return c.json({
      error: 'Internal Server Error',
      message: 'Failed to clear logs'
    }, 500);
  }
});

/**
 * GET /logs/export - Export logs as JSON
 */
app.get('/export', async (c) => {
  try {
    const logKV = new LogKV(c.env.FAIL_LOGS_KV);
    const format = c.req.query('format') || 'json';
    const limit = parseInt(c.req.query('limit')) || 100;
    
    if (limit < 1 || limit > 1000) {
      throw new ValidationError('Export limit must be between 1 and 1000');
    }

    const logs = await logKV.getRecentLogs(limit);
    
    if (format === 'csv') {
      // Convert to CSV format
      const csvHeaders = 'ID,Task ID,Task Name,Type,Level,Status,Message,Timestamp,Execution Time,Retry Count\n';
      const csvRows = logs.map(log => 
        `"${log.id}","${log.taskId}","${log.taskName}","${log.type}","${log.level}","${log.status}","${log.message.replace(/"/g, '""')}","${log.timestamp}","${log.executionTime}","${log.retryCount}"`
      ).join('\n');
      
      const csvContent = csvHeaders + csvRows;
      
      return new Response(csvContent, {
        headers: {
          'Content-Type': 'text/csv',
          'Content-Disposition': `attachment; filename="cron-task-logs-${new Date().toISOString().split('T')[0]}.csv"`
        }
      });
    } else {
      // JSON format
      return c.json({
        success: true,
        data: logs,
        exportInfo: {
          format: 'json',
          count: logs.length,
          exportedAt: new Date().toISOString()
        }
      });
    }

  } catch (error) {
    if (error instanceof ValidationError) {
      throw error;
    }
    
    console.error('Export logs error:', error);
    return c.json({
      error: 'Internal Server Error',
      message: 'Failed to export logs'
    }, 500);
  }
});

/**
 * Helper function to calculate time ago
 * @param {Date} date - Date to calculate from
 * @returns {string} - Human readable time ago
 */
function getTimeAgo(date) {
  const now = new Date();
  const diffMs = now.getTime() - date.getTime();
  const diffSecs = Math.floor(diffMs / 1000);
  const diffMins = Math.floor(diffSecs / 60);
  const diffHours = Math.floor(diffMins / 60);
  const diffDays = Math.floor(diffHours / 24);

  if (diffSecs < 60) {
    return `${diffSecs} seconds ago`;
  } else if (diffMins < 60) {
    return `${diffMins} minutes ago`;
  } else if (diffHours < 24) {
    return `${diffHours} hours ago`;
  } else {
    return `${diffDays} days ago`;
  }
}

/**
 * Helper function to determine log severity
 * @param {string} level - Log level
 * @param {string} status - Log status
 * @returns {string} - Severity level
 */
function getSeverity(level, status) {
  if (status === 'failure' || status === 'timeout' || level === 'error') {
    return 'high';
  } else if (status === 'retry' || level === 'warn') {
    return 'medium';
  } else {
    return 'low';
  }
}

export default app;
