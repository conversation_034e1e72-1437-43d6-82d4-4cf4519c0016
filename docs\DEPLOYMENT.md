# Deployment Guide

This guide covers deploying the Cron Task Manager to Cloudflare's platform.

## Prerequisites

1. **Cloudflare Account**: Sign up at [cloudflare.com](https://cloudflare.com)
2. **Node.js**: Version 18 or higher
3. **Wrangler CLI**: Install globally with `npm install -g wrangler`
4. **Git**: For version control

## Initial Setup

### 1. <PERSON><PERSON> and Install Dependencies

```bash
git clone <repository-url>
cd cron-task
```

Install backend dependencies:
```bash
cd worker
npm install
```

Install frontend dependencies:
```bash
cd ../frontend
npm install
```

### 2. Cloudflare Authentication

Login to Cloudflare:
```bash
wrangler login
```

This will open a browser window for authentication.

## Backend Deployment (Cloudflare Workers)

### 1. Create KV Namespaces

Create the required KV namespaces:

```bash
cd worker

# Create production KV namespaces
wrangler kv:namespace create "TASKS_KV"
wrangler kv:namespace create "FAIL_LOGS_KV"

# Create preview KV namespaces for development
wrangler kv:namespace create "TASKS_KV" --preview
wrangler kv:namespace create "FAIL_LOGS_KV" --preview
```

### 2. Update wrangler.toml

Update the `wrangler.toml` file with the KV namespace IDs returned from the previous step:

```toml
[[kv_namespaces]]
binding = "TASKS_KV"
id = "your_tasks_kv_id_here"
preview_id = "your_tasks_kv_preview_id_here"

[[kv_namespaces]]
binding = "FAIL_LOGS_KV"
id = "your_fail_logs_kv_id_here"
preview_id = "your_fail_logs_kv_preview_id_here"
```

### 3. Set Environment Variables

Set the required secrets using Wrangler:

```bash
# Set the admin password
wrangler secret put PASSWORD

# Set JWT secret (generate a strong random string)
wrangler secret put JWT_SECRET

# Optional: Set Cloudflare API token for dynamic cron updates
wrangler secret put CLOUDFLARE_API_TOKEN

# Optional: Set account and zone IDs
wrangler secret put ACCOUNT_ID
wrangler secret put ZONE_ID

# Optional: Email service configuration
wrangler secret put EMAIL_SERVICE_URL
wrangler secret put EMAIL_API_KEY
wrangler secret put DEFAULT_FROM_EMAIL
```

### 4. Deploy Worker

Deploy the worker:

```bash
wrangler deploy
```

Note the worker URL from the deployment output.

## Frontend Deployment (Cloudflare Pages)

### 1. Create Pages Project

Create a new Pages project:

```bash
cd ../frontend
npx wrangler pages project create cron-task-frontend
```

### 2. Configure Environment Variables

Create production environment file:

```bash
cp .env.example .env.production
```

Update `.env.production` with your worker URL:

```env
VITE_API_BASE_URL=https://your-worker.your-subdomain.workers.dev/api
VITE_APP_TITLE=Cron Task Manager
VITE_ENABLE_DEBUG=false
VITE_ENABLE_ANALYTICS=true
```

### 3. Build and Deploy

Build the frontend:

```bash
npm run build
```

Deploy to Pages:

```bash
npx wrangler pages deploy dist --project-name cron-task-frontend
```

## Configuration

### Worker Configuration

The worker can be configured through environment variables in `wrangler.toml`:

```toml
[vars]
ENVIRONMENT = "production"
MAX_RETRY_COUNT = "3"
DEFAULT_TIMEOUT = "30000"
LOG_RETENTION_COUNT = "50"
```

### Cron Triggers

The system uses three cron triggers:

1. **Main Scheduler** (`*/1 * * * *`): Runs every minute to check for tasks
2. **Backup Scheduler** (`*/5 * * * *`): Runs every 5 minutes as a safety net
3. **Watchdog** (`0 */1 * * *`): Runs hourly for system maintenance

You can adjust these in `wrangler.toml`:

```toml
[[triggers.crons]]
cron = "*/1 * * * *"  # Main scheduler

[[triggers.crons]]
cron = "*/5 * * * *"  # Backup scheduler

[[triggers.crons]]
cron = "0 */1 * * *"  # Watchdog
```

## Security Configuration

### 1. WAF Rules (Optional but Recommended)

In the Cloudflare dashboard, navigate to Security > WAF and create rate limiting rules:

1. **Login Protection**:
   - Path: `/api/auth/login`
   - Rate: 5 requests per 15 minutes per IP

2. **API Protection**:
   - Path: `/api/*`
   - Rate: 100 requests per minute per IP

### 2. Access Control

Consider setting up Cloudflare Access for additional security:

1. Go to Zero Trust > Access > Applications
2. Create a new application for your Pages domain
3. Set up authentication policies

## Monitoring and Logging

### 1. Worker Analytics

Monitor your worker performance in the Cloudflare dashboard:
- Go to Workers & Pages > Your Worker > Metrics

### 2. Real User Monitoring (RUM)

Enable RUM for your Pages site:
- Go to Speed > Optimization > Real User Monitoring

### 3. Alerts

Set up alerts for:
- Worker errors
- High response times
- Failed cron triggers

## Troubleshooting

### Common Issues

1. **KV Namespace Errors**:
   - Verify KV namespace IDs in `wrangler.toml`
   - Ensure namespaces exist in your account

2. **Authentication Errors**:
   - Check that `PASSWORD` and `JWT_SECRET` secrets are set
   - Verify secrets with `wrangler secret list`

3. **Cron Not Triggering**:
   - Check Worker logs for errors
   - Verify cron expressions are valid
   - Ensure Worker is deployed successfully

4. **Frontend API Errors**:
   - Verify `VITE_API_BASE_URL` points to correct Worker URL
   - Check CORS configuration in Worker
   - Ensure Worker is accessible

### Debugging

1. **View Worker Logs**:
   ```bash
   wrangler tail
   ```

2. **Test Worker Locally**:
   ```bash
   cd worker
   npm run dev
   ```

3. **Test Frontend Locally**:
   ```bash
   cd frontend
   npm run dev
   ```

## Scaling Considerations

### Worker Limits

- **CPU Time**: 50ms per request (can be increased with paid plans)
- **Memory**: 128MB per request
- **Subrequests**: 50 per request

### KV Limits

- **Read Operations**: 10M per day (free tier)
- **Write Operations**: 1M per day (free tier)
- **Storage**: 1GB (free tier)

### Optimization Tips

1. **Batch Operations**: Group multiple KV operations when possible
2. **Caching**: Use appropriate cache headers and KV TTL
3. **Error Handling**: Implement proper error handling to avoid unnecessary retries
4. **Monitoring**: Set up alerts for quota usage

## Backup and Recovery

### 1. KV Data Backup

Regularly backup your KV data:

```bash
# Export tasks
wrangler kv:key list --namespace-id=your_tasks_kv_id > tasks_backup.json

# Export logs
wrangler kv:key list --namespace-id=your_fail_logs_kv_id > logs_backup.json
```

### 2. Configuration Backup

Keep your `wrangler.toml` and environment files in version control.

### 3. Recovery Process

1. Recreate KV namespaces if needed
2. Restore data from backups
3. Redeploy Worker and Pages
4. Update DNS if necessary

## Updates and Maintenance

### 1. Worker Updates

```bash
cd worker
npm update
wrangler deploy
```

### 2. Frontend Updates

```bash
cd frontend
npm update
npm run build
npx wrangler pages deploy dist --project-name cron-task-frontend
```

### 3. Monitoring Updates

- Check Cloudflare changelog for platform updates
- Monitor dependency security advisories
- Review Worker analytics for performance trends

## Support

For issues and questions:

1. Check the troubleshooting section above
2. Review Cloudflare documentation
3. Check project issues on GitHub
4. Contact support through appropriate channels
