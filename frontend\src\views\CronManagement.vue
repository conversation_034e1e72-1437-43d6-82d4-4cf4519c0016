<template>
  <div class="cron-management">
    <div class="page-header">
      <h1>Cron 触发器管理</h1>
      <p class="page-description">
        管理和监控动态 Cron 触发器，优化任务调度性能
      </p>
    </div>

    <!-- Status Overview -->
    <el-row :gutter="20" class="status-cards">
      <el-col :span="6">
        <el-card class="status-card">
          <div class="status-item">
            <div class="status-icon">
              <el-icon><Timer /></el-icon>
            </div>
            <div class="status-content">
              <div class="status-value">{{ cronStatus?.currentTriggers?.length || 0 }}</div>
              <div class="status-label">当前触发器</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card class="status-card">
          <div class="status-item">
            <div class="status-icon" :class="{ 'status-success': cronStatus?.isConfigured }">
              <el-icon><Setting /></el-icon>
            </div>
            <div class="status-content">
              <div class="status-value">{{ cronStatus?.isConfigured ? '已配置' : '未配置' }}</div>
              <div class="status-label">API 状态</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card class="status-card">
          <div class="status-item">
            <div class="status-icon" :class="{ 'status-success': cronStatus?.dynamicUpdatesEnabled }">
              <el-icon><Refresh /></el-icon>
            </div>
            <div class="status-content">
              <div class="status-value">{{ cronStatus?.dynamicUpdatesEnabled ? '启用' : '禁用' }}</div>
              <div class="status-label">动态更新</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card class="status-card">
          <div class="status-item">
            <div class="status-icon" :class="{ 'status-success': cronStatus?.canUpdateNow }">
              <el-icon><Clock /></el-icon>
            </div>
            <div class="status-content">
              <div class="status-value">{{ cronStatus?.canUpdateNow ? '可更新' : '限制中' }}</div>
              <div class="status-label">更新状态</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- Strategy Information -->
    <el-card class="section-card strategy-info">
      <template #header>
        <div class="card-header">
          <span>调度策略</span>
          <el-tag :type="getStrategyTagType()" size="large">
            {{ getStrategyDisplayName() }}
          </el-tag>
        </div>
      </template>

      <div class="strategy-description">
        <div class="strategy-details">
          <h4>{{ getStrategyDisplayName() }}</h4>
          <p>{{ getStrategyDescription() }}</p>

          <div v-if="cronStatus?.strategy === 'exact_match' && cronStatus?.nextTasks?.length" class="next-tasks">
            <h5>即将执行的任务：</h5>
            <div class="task-list">
              <div
                v-for="(taskInfo, index) in cronStatus.nextTasks"
                :key="index"
                class="task-item"
              >
                <div class="task-info">
                  <span class="task-name">{{ taskInfo.task?.name || '未知任务' }}</span>
                  <span class="task-cron">{{ taskInfo.cronExpression }}</span>
                </div>
                <div class="task-time">
                  {{ formatNextRunTime(taskInfo.nextRunTime) }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-card>

    <el-row :gutter="20" class="status-cards">
    </el-row>

    <!-- Current Triggers -->
    <el-card class="section-card">
      <template #header>
        <div class="card-header">
          <span>当前触发器</span>
          <div class="header-actions">
            <el-button
              type="primary"
              :icon="Refresh"
              @click="refreshStatus"
              :loading="loading.status"
            >
              刷新
            </el-button>
            <el-button
              type="success"
              :icon="Cpu"
              @click="optimizeTriggers"
              :loading="loading.optimize"
              :disabled="!cronStatus?.isConfigured"
            >
              智能优化
            </el-button>
          </div>
        </div>
      </template>

      <div v-if="cronStatus?.currentTriggers?.length" class="triggers-list">
        <div
          v-for="(trigger, index) in cronStatus.currentTriggers"
          :key="index"
          class="trigger-item"
        >
          <div class="trigger-info">
            <div class="trigger-expression">{{ trigger }}</div>
            <div class="trigger-description">{{ describeCronExpression(trigger) }}</div>
          </div>
          <div class="trigger-type">
            <el-tag :type="getTriggerTypeColor(index)">
              {{ getTriggerTypeName(index) }}
            </el-tag>
          </div>
        </div>
      </div>

      <el-empty v-else description="暂无触发器配置" />
    </el-card>

    <!-- Optimization Preview -->
    <el-card class="section-card" v-if="previewData">
      <template #header>
        <span>优化建议</span>
      </template>

      <div class="optimization-preview">
        <div class="preview-item">
          <label>当前主触发器:</label>
          <code>{{ previewData.currentMainTrigger }}</code>
        </div>

        <div class="preview-item">
          <label>建议触发器:</label>
          <code>{{ previewData.optimalCron }}</code>
          <el-tag
            v-if="previewData.isOptimal"
            type="success"
            size="small"
            style="margin-left: 8px"
          >
            已优化
          </el-tag>
        </div>

        <div class="preview-item">
          <label>描述:</label>
          <span>{{ previewData.description }}</span>
        </div>

        <div class="preview-item">
          <label>活跃任务数:</label>
          <span>{{ previewData.activeTaskCount }}</span>
        </div>

        <div class="preview-item">
          <label>建议:</label>
          <span>{{ previewData.recommendation }}</span>
        </div>

        <div v-if="!previewData.isOptimal" class="preview-actions">
          <el-button
            type="primary"
            @click="applyOptimization"
            :loading="loading.apply"
          >
            应用优化
          </el-button>
        </div>
      </div>
    </el-card>

    <!-- Manual Update -->
    <el-card class="section-card">
      <template #header>
        <span>手动更新触发器</span>
      </template>

      <div class="manual-update">
        <div class="update-form">
          <div
            v-for="(trigger, index) in manualTriggers"
            :key="index"
            class="trigger-input-group"
          >
            <el-input
              v-model="manualTriggers[index]"
              placeholder="输入 Cron 表达式，如: */5 * * * *"
              @blur="validateTrigger(index)"
            >
              <template #prepend>触发器 {{ index + 1 }}</template>
              <template #append>
                <el-button
                  v-if="manualTriggers.length > 1"
                  :icon="Delete"
                  @click="removeTrigger(index)"
                />
              </template>
            </el-input>

            <div v-if="triggerValidations[index]" class="trigger-validation">
              <el-alert
                v-if="!triggerValidations[index].valid"
                :title="triggerValidations[index].error"
                type="error"
                size="small"
                show-icon
                :closable="false"
              />
              <div v-else class="trigger-preview">
                <span class="preview-description">
                  {{ describeCronExpression(manualTriggers[index]) }}
                </span>
              </div>
            </div>
          </div>

          <div class="form-actions">
            <el-button
              :icon="Plus"
              @click="addTrigger"
              :disabled="manualTriggers.length >= 10"
            >
              添加触发器
            </el-button>

            <el-button
              type="primary"
              @click="updateTriggers"
              :loading="loading.update"
              :disabled="!isFormValid || !cronStatus?.isConfigured"
            >
              更新触发器
            </el-button>
          </div>
        </div>
      </div>
    </el-card>

    <!-- Update History -->
    <el-card class="section-card">
      <template #header>
        <span>更新历史</span>
      </template>

      <el-table
        :data="updateHistory"
        v-loading="loading.history"
        empty-text="暂无更新历史"
      >
        <el-table-column prop="timestamp" label="时间" width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.timestamp) }}
          </template>
        </el-table-column>

        <el-table-column prop="reason" label="原因" width="150">
          <template #default="{ row }">
            <el-tag size="small">{{ row.reason }}</el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="success" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="row.success ? 'success' : 'danger'" size="small">
              {{ row.success ? '成功' : '失败' }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="oldTriggers" label="旧触发器">
          <template #default="{ row }">
            <div class="triggers-display">
              <code v-for="trigger in row.oldTriggers" :key="trigger">
                {{ trigger }}
              </code>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="newTriggers" label="新触发器">
          <template #default="{ row }">
            <div class="triggers-display">
              <code v-for="trigger in row.newTriggers" :key="trigger">
                {{ trigger }}
              </code>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Timer, Setting, Refresh, Clock, Cpu, Plus, Delete
} from '@element-plus/icons-vue'
import {
  getCronStatus,
  getCronHistory,
  updateCronTriggers,
  optimizeCronTriggers,
  previewOptimalCron,
  describeCronExpression,
  validateCronFormat
} from '@/api/cron.js'

// Reactive data
const cronStatus = ref(null)
const updateHistory = ref([])
const previewData = ref(null)
const manualTriggers = ref(['*/5 * * * *', '*/5 * * * *', '0 */1 * * *'])
const triggerValidations = ref({})

const loading = reactive({
  status: false,
  history: false,
  optimize: false,
  apply: false,
  update: false
})

// Computed properties
const isFormValid = computed(() => {
  return manualTriggers.value.every((trigger, index) => {
    const validation = triggerValidations.value[index]
    return validation && validation.valid
  })
})

// Strategy related methods
const getStrategyTagType = () => {
  const strategy = cronStatus.value?.strategy || 'frequency_optimization'
  return strategy === 'exact_match' ? 'success' : 'primary'
}

const getStrategyDisplayName = () => {
  const strategy = cronStatus.value?.strategy || 'frequency_optimization'
  return strategy === 'exact_match' ? '精确匹配' : '频率优化'
}

const getStrategyDescription = () => {
  const strategy = cronStatus.value?.strategy || 'frequency_optimization'
  if (strategy === 'exact_match') {
    return '触发器直接设置为最近需要执行的任务的 cron 表达式，实现精确时间匹配。'
  } else {
    return '根据任务模式和系统负载智能计算最优调度频率，实现资源优化。'
  }
}

const formatNextRunTime = (timeStr) => {
  if (!timeStr) return '未知'
  try {
    const date = new Date(timeStr)
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    })
  } catch (error) {
    return '时间格式错误'
  }
}

// Methods
const refreshStatus = async () => {
  loading.status = true
  try {
    const [statusData, historyData, previewDataResult] = await Promise.all([
      getCronStatus(),
      getCronHistory(),
      previewOptimalCron()
    ])

    cronStatus.value = statusData.data
    updateHistory.value = historyData.data
    previewData.value = previewDataResult.data
  } catch (error) {
    ElMessage.error('获取状态失败: ' + error.message)
  } finally {
    loading.status = false
  }
}

const optimizeTriggers = async () => {
  loading.optimize = true
  try {
    const result = await optimizeCronTriggers()

    if (result.success) {
      ElMessage.success('触发器优化成功')
      await refreshStatus()
    } else {
      ElMessage.warning(result.message || '优化失败')
    }
  } catch (error) {
    ElMessage.error('优化失败: ' + error.message)
  } finally {
    loading.optimize = false
  }
}

const applyOptimization = async () => {
  if (!previewData.value) return

  loading.apply = true
  try {
    const result = await updateCronTriggers(
      previewData.value.suggestedTriggers,
      'manual_optimization'
    )

    if (result.success) {
      ElMessage.success('优化应用成功')
      await refreshStatus()
    } else {
      ElMessage.warning(result.message || '应用失败')
    }
  } catch (error) {
    ElMessage.error('应用失败: ' + error.message)
  } finally {
    loading.apply = false
  }
}

const updateTriggers = async () => {
  if (!isFormValid.value) {
    ElMessage.error('请修正表单错误后再提交')
    return
  }

  const validTriggers = manualTriggers.value.filter(trigger => trigger.trim())

  if (validTriggers.length === 0) {
    ElMessage.error('至少需要一个有效的触发器')
    return
  }

  try {
    await ElMessageBox.confirm(
      '确定要更新触发器配置吗？这将影响任务调度。',
      '确认更新',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    loading.update = true
    const result = await updateCronTriggers(validTriggers, 'manual')

    if (result.success) {
      ElMessage.success('触发器更新成功')
      await refreshStatus()
    } else {
      ElMessage.warning(result.message || '更新失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('更新失败: ' + error.message)
    }
  } finally {
    loading.update = false
  }
}

const validateTrigger = (index) => {
  const trigger = manualTriggers.value[index]
  if (!trigger || !trigger.trim()) {
    triggerValidations.value[index] = { valid: true }
    return
  }

  const validation = validateCronFormat(trigger.trim())
  triggerValidations.value[index] = validation
}

const addTrigger = () => {
  if (manualTriggers.value.length < 10) {
    manualTriggers.value.push('')
  }
}

const removeTrigger = (index) => {
  if (manualTriggers.value.length > 1) {
    manualTriggers.value.splice(index, 1)
    delete triggerValidations.value[index]
  }
}

const getTriggerTypeName = (index) => {
  const names = ['主调度器', '备用调度器', '看门狗']
  return names[index] || `触发器 ${index + 1}`
}

const getTriggerTypeColor = (index) => {
  const colors = ['primary', 'warning', 'info']
  return colors[index] || 'default'
}

const formatDateTime = (dateString) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

// Initialize
onMounted(() => {
  refreshStatus()

  // Validate initial triggers
  manualTriggers.value.forEach((_, index) => {
    validateTrigger(index)
  })
})
</script>

<style scoped>
.cron-management {
  padding: 20px;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.page-description {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.status-cards {
  margin-bottom: 24px;
}

.status-card {
  height: 100px;
}

.status-item {
  display: flex;
  align-items: center;
  height: 100%;
}

.status-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  background: #f5f7fa;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 24px;
  color: #909399;
}

.status-icon.status-success {
  background: #f0f9ff;
  color: #67c23a;
}

.status-content {
  flex: 1;
}

.status-value {
  font-size: 20px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.status-label {
  font-size: 12px;
  color: #909399;
}

.section-card {
  margin-bottom: 24px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.triggers-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.trigger-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  background: #fafafa;
}

.trigger-info {
  flex: 1;
}

.trigger-expression {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.trigger-description {
  font-size: 12px;
  color: #606266;
}

.optimization-preview {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.preview-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.preview-item label {
  min-width: 120px;
  font-weight: 500;
  color: #606266;
}

.preview-item code {
  background: #f5f7fa;
  padding: 4px 8px;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
}

.preview-actions {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #ebeef5;
}

.manual-update {
  max-width: 800px;
}

.trigger-input-group {
  margin-bottom: 16px;
}

.trigger-validation {
  margin-top: 8px;
}

.trigger-preview {
  padding: 8px 12px;
  background: #f0f9ff;
  border-radius: 4px;
  border-left: 3px solid #409eff;
}

.preview-description {
  font-size: 13px;
  color: #606266;
}

.form-actions {
  display: flex;
  gap: 12px;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.triggers-display {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.triggers-display code {
  background: #f5f7fa;
  padding: 2px 6px;
  border-radius: 3px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  color: #303133;
}

:deep(.el-table) {
  font-size: 13px;
}

:deep(.el-table .cell) {
  padding: 8px 0;
}

:deep(.el-input-group__prepend) {
  background: #f5f7fa;
  color: #606266;
  font-weight: 500;
}

:deep(.el-card__header) {
  padding: 16px 20px;
  border-bottom: 1px solid #ebeef5;
}

:deep(.el-card__body) {
  padding: 20px;
}

.strategy-info {
  margin-bottom: 20px;
}

.strategy-description {
  padding: 16px 0;
}

.strategy-details h4 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.strategy-details p {
  margin: 0 0 16px 0;
  color: #606266;
  line-height: 1.6;
}

.next-tasks h5 {
  margin: 0 0 12px 0;
  color: #303133;
  font-size: 14px;
  font-weight: 600;
}

.task-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.task-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: #f5f7fa;
  border-radius: 6px;
  border-left: 3px solid #409eff;
}

.task-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.task-name {
  font-weight: 600;
  color: #303133;
  font-size: 14px;
}

.task-cron {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  color: #909399;
  font-size: 12px;
  background: #fff;
  padding: 2px 6px;
  border-radius: 3px;
  border: 1px solid #dcdfe6;
}

.task-time {
  color: #67c23a;
  font-size: 12px;
  font-weight: 500;
}
</style>
