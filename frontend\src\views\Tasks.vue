<template>
  <div class="tasks-container">
    <!-- Page Header -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">
          <el-icon><Timer /></el-icon>
          {{ $t('tasks.title') }}
        </h1>
        <div class="header-actions">
          <el-button :icon="Refresh" @click="refreshTasks" :loading="tasksStore.loading">
            {{ $t('common.refresh') }}
          </el-button>
          <el-button type="primary" :icon="Plus" @click="showCreateDialog">
            {{ $t('tasks.createTask') }}
          </el-button>
        </div>
      </div>
    </div>

    <!-- Filters -->
    <div class="filters-section">
      <el-card class="filters-card">
        <div class="filters-content">
          <el-input
            v-model="tasksStore.filters.search"
            :placeholder="$t('tasks.searchPlaceholder')"
            :prefix-icon="Search"
            clearable
            style="width: 300px"
          />

          <el-select
            v-model="tasksStore.filters.status"
            :placeholder="$t('tasks.filterByStatus')"
            clearable
            style="width: 150px"
          >
            <el-option :label="$t('tasks.status.active')" value="active" />
            <el-option :label="$t('tasks.status.inactive')" value="inactive" />
            <el-option :label="$t('tasks.status.paused')" value="paused" />
          </el-select>

          <el-select
            v-model="tasksStore.filters.type"
            :placeholder="$t('tasks.filterByType')"
            clearable
            style="width: 180px"
          >
            <el-option
              v-for="type in taskTypes"
              :key="type.type"
              :label="$t(`tasks.types.${type.type}`)"
              :value="type.type"
            />
          </el-select>

          <el-button @click="tasksStore.clearFilters()">
            {{ $t('common.clearFilters') }}
          </el-button>
        </div>
      </el-card>
    </div>

    <!-- Tasks Table -->
    <el-card class="tasks-table-card">
      <div v-if="tasksStore.loading && tasksStore.tasks.length === 0" class="loading-container">
        <el-icon class="is-loading"><Loading /></el-icon>
        <span class="loading-text">{{ $t('tasks.loadingText') }}</span>
      </div>

      <div v-else-if="filteredTasks.length === 0" class="empty-state">
        <el-icon class="empty-icon"><Timer /></el-icon>
        <div class="empty-title">{{ $t('tasks.noTasks') }}</div>
        <div class="empty-description">
          {{ tasksStore.tasks.length === 0 ? $t('tasks.noTasksDesc') : $t('tasks.noTasksFiltered') }}
        </div>
        <el-button v-if="tasksStore.tasks.length === 0" type="primary" @click="showCreateDialog">
          {{ $t('tasks.createFirstTask') }}
        </el-button>
      </div>

      <el-table
        v-else
        :data="filteredTasks"
        class="tasks-table"
        stripe
        @row-click="handleRowClick"
      >
        <el-table-column prop="name" :label="$t('tasks.table.name')" min-width="200">
          <template #default="{ row }">
            <div class="task-name">
              <span class="name-text">{{ row.name }}</span>
              <div v-if="row.description" class="task-description">
                {{ row.description }}
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="type" :label="$t('tasks.table.type')" width="150">
          <template #default="{ row }">
            <div class="task-type">
              <el-icon><component :is="getTypeIcon(row.type)" /></el-icon>
              <span>{{ row.executorName || $t(`tasks.types.${row.type}`) }}</span>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="status" :label="$t('tasks.table.status')" width="120">
          <template #default="{ row }">
            <el-tag
              :type="getStatusType(row.status)"
              :class="`status-${row.status}`"
              size="small"
            >
              {{ $t(`tasks.status.${row.status}`) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="cron" :label="$t('tasks.table.schedule')" width="150">
          <template #default="{ row }">
            <div class="schedule-info">
              <code class="cron-expression">{{ row.cron }}</code>
              <div v-if="row.nextRunAt" class="next-run">
                {{ $t('tasks.table.nextRun') }}: {{ formatNextRun(row.nextRunAt) }}
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="lastRunAt" :label="$t('tasks.table.lastRun')" width="150">
          <template #default="{ row }">
            <span v-if="row.lastRunAt" class="last-run">
              {{ formatLastRun(row.lastRunAt) }}
            </span>
            <span v-else class="never-run">{{ $t('time.never') }}</span>
          </template>
        </el-table-column>

        <el-table-column :label="$t('tasks.table.actions')" width="200" fixed="right">
          <template #default="{ row }">
            <div class="action-buttons">
              <el-tooltip :content="$t('tasks.actions.run')">
                <el-button
                  :icon="VideoPlay"
                  circle
                  size="small"
                  type="primary"
                  :disabled="row.status !== 'active'"
                  @click.stop="runTask(row)"
                />
              </el-tooltip>

              <el-tooltip :content="$t('tasks.actions.edit')">
                <el-button
                  :icon="Edit"
                  circle
                  size="small"
                  @click.stop="editTask(row)"
                />
              </el-tooltip>

              <el-tooltip :content="row.status === 'active' ? $t('tasks.actions.pause') : $t('tasks.actions.resume')">
                <el-button
                  :icon="row.status === 'active' ? VideoPause : VideoPlay"
                  circle
                  size="small"
                  :type="row.status === 'active' ? 'warning' : 'success'"
                  @click.stop="toggleTaskStatus(row)"
                />
              </el-tooltip>

              <el-tooltip :content="$t('tasks.actions.delete')">
                <el-button
                  :icon="Delete"
                  circle
                  size="small"
                  type="danger"
                  @click.stop="deleteTask(row)"
                />
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- Create/Edit Dialog -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogMode === 'create' ? $t('tasks.createTask') : $t('tasks.actions.edit')"
      width="800px"
      :close-on-click-modal="false"
    >
      <TaskForm
        v-if="dialogVisible"
        ref="taskFormRef"
        :task="currentTask"
        :task-types="taskTypes"
        :mode="dialogMode"
        @submit="handleTaskSubmit"
        @cancel="closeDialog"
      />
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useTasksStore } from '@/stores/tasks'
import { useQuery } from '@tanstack/vue-query'
import { tasksApi } from '@/api/tasks'
import TaskForm from '@/components/TaskForm.vue'
import {
  Timer, Plus, Refresh, Search, Loading, VideoPlay, VideoPause,
  Edit, Delete, Promotion, Comment, Message
} from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import dayjs from '@/utils/dayjs'

const tasksStore = useTasksStore()

// Dialog state
const dialogVisible = ref(false)
const dialogMode = ref('create') // 'create' or 'edit'
const currentTask = ref(null)
const taskFormRef = ref()

// Fetch task types
const { data: taskTypes } = useQuery({
  queryKey: ['taskTypes'],
  queryFn: () => tasksApi.getTaskTypes().then(res => res.data),
  staleTime: Infinity // Task types don't change often
})

// Computed
const filteredTasks = computed(() => tasksStore.filteredTasks)

// Methods
const refreshTasks = () => {
  tasksStore.fetchTasks()
}

const showCreateDialog = () => {
  dialogMode.value = 'create'
  currentTask.value = null
  dialogVisible.value = true
}

const editTask = (task) => {
  dialogMode.value = 'edit'
  currentTask.value = { ...task }
  dialogVisible.value = true
}

const closeDialog = () => {
  dialogVisible.value = false
  currentTask.value = null
}

const handleTaskSubmit = async (taskData) => {
  try {
    if (dialogMode.value === 'create') {
      await tasksStore.createTask(taskData)
    } else {
      await tasksStore.updateTask(currentTask.value.id, taskData)
    }
    closeDialog()
  } catch (error) {
    console.error('Task submit error:', error)
  }
}

const runTask = async (task) => {
  try {
    await tasksStore.runTask(task.id)
  } catch (error) {
    console.error('Run task error:', error)
  }
}

const toggleTaskStatus = async (task) => {
  const newStatus = task.status === 'active' ? 'paused' : 'active'
  try {
    await tasksStore.updateTask(task.id, { status: newStatus })
  } catch (error) {
    console.error('Toggle status error:', error)
  }
}

const deleteTask = async (task) => {
  try {
    await ElMessageBox.confirm(
      `This will permanently delete the task "${task.name}". Continue?`,
      'Warning',
      {
        confirmButtonText: 'Delete',
        cancelButtonText: 'Cancel',
        type: 'warning'
      }
    )

    await tasksStore.deleteTask(task.id)
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Delete task error:', error)
    }
  }
}

const handleRowClick = (row) => {
  editTask(row)
}

// Utility functions
const formatTaskType = (type) => {
  return type.split('_').map(word =>
    word.charAt(0).toUpperCase() + word.slice(1)
  ).join(' ')
}

const formatStatus = (status) => {
  return status.charAt(0).toUpperCase() + status.slice(1)
}

const getStatusType = (status) => {
  switch (status) {
    case 'active': return 'success'
    case 'paused': return 'warning'
    case 'inactive': return 'info'
    default: return 'info'
  }
}

const getTypeIcon = (type) => {
  switch (type) {
    case 'http_request': return Promotion
    case 'Comment': return Comment
    case 'email': return Message
    default: return Timer
  }
}

const formatNextRun = (timestamp) => {
  if (!timestamp) return 'Not scheduled'
  return dayjs(timestamp).format('MMM D, HH:mm')
}

const formatLastRun = (timestamp) => {
  if (!timestamp) return 'Never'
  return dayjs(timestamp).fromNow()
}

// Initialize
onMounted(() => {
  tasksStore.fetchTasks()
  tasksStore.fetchTaskTypes()
})
</script>

<style lang="scss" scoped>
.tasks-container {
  max-width: 1400px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: var(--app-spacing-lg);
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.page-title {
  display: flex;
  align-items: center;
  gap: var(--app-spacing-sm);
  margin: 0;
  font-size: 28px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.header-actions {
  display: flex;
  gap: var(--app-spacing-sm);
}

.filters-section {
  margin-bottom: var(--app-spacing-lg);
}

.filters-content {
  display: flex;
  align-items: center;
  gap: var(--app-spacing-md);
  flex-wrap: wrap;
}

.tasks-table-card {
  .el-card__body {
    padding: 0;
  }
}

.tasks-table {
  .task-name {
    .name-text {
      font-weight: 500;
      color: var(--el-text-color-primary);
    }

    .task-description {
      font-size: 12px;
      color: var(--el-text-color-secondary);
      margin-top: 2px;
      line-height: 1.3;
    }
  }

  .task-type {
    display: flex;
    align-items: center;
    gap: var(--app-spacing-xs);
    font-size: 14px;
  }

  .schedule-info {
    .cron-expression {
      font-family: 'Courier New', monospace;
      font-size: 12px;
      background: var(--el-fill-color-light);
      padding: 2px 4px;
      border-radius: 3px;
    }

    .next-run {
      font-size: 11px;
      color: var(--el-text-color-secondary);
      margin-top: 2px;
    }
  }

  .last-run {
    font-size: 12px;
    color: var(--el-text-color-secondary);
  }

  .never-run {
    font-size: 12px;
    color: var(--el-text-color-placeholder);
    font-style: italic;
  }

  .action-buttons {
    display: flex;
    gap: var(--app-spacing-xs);
  }

  :deep(.el-table__row) {
    cursor: pointer;

    &:hover {
      background-color: var(--el-fill-color-light);
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--app-spacing-md);
  }

  .header-actions {
    width: 100%;
    justify-content: flex-end;
  }

  .filters-content {
    flex-direction: column;
    align-items: stretch;

    .el-input,
    .el-select {
      width: 100% !important;
    }
  }

  .tasks-table {
    :deep(.el-table__header),
    :deep(.el-table__body) {
      font-size: 12px;
    }
  }
}
</style>
