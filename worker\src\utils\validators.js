/**
 * 配置验证器
 * 为纯自调度模型提供配置验证功能
 */

import { SimplifiedCronParser } from './cron-parser.js';

export class TaskConfigValidator {
  /**
   * 验证完整的任务配置
   * @param {Object} config - 任务配置对象
   * @returns {Object} - 验证结果 {isValid: boolean, errors: string[]}
   */
  static validate(config) {
    const errors = [];

    // 基本结构验证
    if (!config || typeof config !== 'object') {
      errors.push('Configuration must be a valid object');
      return { isValid: false, errors };
    }

    // 必需字段验证
    this.validateRequiredFields(config, errors);
    
    // 任务类型验证
    this.validateTaskType(config, errors);
    
    // Cron表达式验证
    this.validateCronExpression(config, errors);
    
    // 启用状态验证
    this.validateEnabledFlag(config, errors);
    
    // 超时设置验证
    this.validateTimeout(config, errors);
    
    // 特定任务类型配置验证
    this.validateTypeSpecificConfig(config, errors);

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * 验证必需字段
   */
  static validateRequiredFields(config, errors) {
    const requiredFields = ['type', 'cron', 'enabled'];
    
    for (const field of requiredFields) {
      if (!(field in config)) {
        errors.push(`Missing required field: ${field}`);
      }
    }
  }

  /**
   * 验证任务类型
   */
  static validateTaskType(config, errors) {
    const supportedTypes = ['http', 'webhook'];
    
    if (!config.type) {
      return; // 已在必需字段验证中处理
    }
    
    if (!supportedTypes.includes(config.type)) {
      errors.push(`Unsupported task type: ${config.type}. Supported types: ${supportedTypes.join(', ')}`);
    }
  }

  /**
   * 验证Cron表达式
   */
  static validateCronExpression(config, errors) {
    if (!config.cron) {
      return; // 已在必需字段验证中处理
    }
    
    if (typeof config.cron !== 'string') {
      errors.push('Cron expression must be a string');
      return;
    }
    
    if (!SimplifiedCronParser.isValid(config.cron)) {
      errors.push(`Invalid cron expression: ${config.cron}`);
    }
  }

  /**
   * 验证启用标志
   */
  static validateEnabledFlag(config, errors) {
    if (!('enabled' in config)) {
      return; // 已在必需字段验证中处理
    }
    
    if (typeof config.enabled !== 'boolean') {
      errors.push('Enabled flag must be a boolean value');
    }
  }

  /**
   * 验证超时设置
   */
  static validateTimeout(config, errors) {
    if ('timeout' in config) {
      if (typeof config.timeout !== 'number' || config.timeout <= 0) {
        errors.push('Timeout must be a positive number');
      } else if (config.timeout > 300000) { // 5分钟最大超时
        errors.push('Timeout cannot exceed 300000ms (5 minutes)');
      }
    }
  }

  /**
   * 验证特定任务类型的配置
   */
  static validateTypeSpecificConfig(config, errors) {
    switch (config.type) {
      case 'http':
        this.validateHttpConfig(config, errors);
        break;
      case 'webhook':
        this.validateWebhookConfig(config, errors);
        break;
    }
  }

  /**
   * 验证HTTP任务配置
   */
  static validateHttpConfig(config, errors) {
    // URL验证
    if (!config.url) {
      errors.push('HTTP task requires a URL');
      return;
    }
    
    if (typeof config.url !== 'string') {
      errors.push('URL must be a string');
      return;
    }
    
    try {
      new URL(config.url);
    } catch (error) {
      errors.push(`Invalid URL format: ${config.url}`);
    }
    
    // HTTP方法验证
    if (config.method) {
      const validMethods = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'HEAD', 'OPTIONS'];
      if (!validMethods.includes(config.method.toUpperCase())) {
        errors.push(`Invalid HTTP method: ${config.method}`);
      }
    }
    
    // Headers验证
    if (config.headers) {
      if (typeof config.headers !== 'object' || Array.isArray(config.headers)) {
        errors.push('Headers must be an object');
      }
    }
    
    // Body验证
    if (config.body && typeof config.body !== 'object') {
      errors.push('Body must be an object');
    }
  }

  /**
   * 验证Webhook任务配置
   */
  static validateWebhookConfig(config, errors) {
    // Webhook URL验证
    if (!config.webhookUrl) {
      errors.push('Webhook task requires a webhookUrl');
      return;
    }
    
    if (typeof config.webhookUrl !== 'string') {
      errors.push('Webhook URL must be a string');
      return;
    }
    
    try {
      new URL(config.webhookUrl);
    } catch (error) {
      errors.push(`Invalid webhook URL format: ${config.webhookUrl}`);
    }
    
    // Headers验证
    if (config.headers) {
      if (typeof config.headers !== 'object' || Array.isArray(config.headers)) {
        errors.push('Headers must be an object');
      }
    }
    
    // Payload验证
    if (config.payload && typeof config.payload !== 'object') {
      errors.push('Payload must be an object');
    }
  }

  /**
   * 验证任务ID格式
   * @param {string} taskId - 任务ID
   * @returns {boolean} - 是否有效
   */
  static isValidTaskId(taskId) {
    if (!taskId || typeof taskId !== 'string') {
      return false;
    }
    
    // 任务ID应该是字母数字字符和连字符的组合，长度在3-50之间
    const taskIdPattern = /^[a-zA-Z0-9_-]{3,50}$/;
    return taskIdPattern.test(taskId);
  }

  /**
   * 清理和标准化配置
   * @param {Object} config - 原始配置
   * @returns {Object} - 清理后的配置
   */
  static sanitizeConfig(config) {
    const sanitized = { ...config };
    
    // 标准化任务类型
    if (sanitized.type) {
      sanitized.type = sanitized.type.toLowerCase();
    }
    
    // 标准化HTTP方法
    if (sanitized.method) {
      sanitized.method = sanitized.method.toUpperCase();
    }
    
    // 设置默认值
    if (!('timeout' in sanitized)) {
      sanitized.timeout = 30000; // 30秒默认超时
    }
    
    if (!('enabled' in sanitized)) {
      sanitized.enabled = true; // 默认启用
    }
    
    // 清理空值
    Object.keys(sanitized).forEach(key => {
      if (sanitized[key] === null || sanitized[key] === undefined) {
        delete sanitized[key];
      }
    });
    
    return sanitized;
  }

  /**
   * 生成配置摘要
   * @param {Object} config - 任务配置
   * @returns {Object} - 配置摘要
   */
  static getConfigSummary(config) {
    const summary = {
      type: config.type,
      enabled: config.enabled,
      schedule: SimplifiedCronParser.getDescription(config.cron),
      timeout: config.timeout || 30000
    };
    
    switch (config.type) {
      case 'http':
        summary.target = config.url;
        summary.method = config.method || 'GET';
        break;
      case 'webhook':
        summary.target = config.webhookUrl;
        summary.method = 'POST';
        break;
    }
    
    return summary;
  }
}

/**
 * 环境变量验证器
 */
export class EnvironmentValidator {
  /**
   * 验证必需的环境变量
   * @param {Object} env - 环境对象
   * @returns {Object} - 验证结果
   */
  static validateEnvironment(env) {
    const errors = [];
    const warnings = [];
    
    // 检查必需的绑定
    if (!env.TASKS_KV) {
      errors.push('Missing TASKS_KV binding');
    }
    
    if (!env.TASK_DO) {
      errors.push('Missing TASK_DO binding');
    }
    
    // 检查可选的环境变量
    if (!env.ENVIRONMENT) {
      warnings.push('ENVIRONMENT variable not set, defaulting to production');
    }
    
    if (!env.LOG_LEVEL) {
      warnings.push('LOG_LEVEL variable not set, defaulting to info');
    }
    
    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }
}
